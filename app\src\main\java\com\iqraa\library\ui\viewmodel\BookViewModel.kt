package com.iqraa.library.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.iqraa.library.data.model.Book
import com.iqraa.library.data.model.Category
import com.iqraa.library.data.repository.BookRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class BookViewModel @Inject constructor(
    private val bookRepository: BookRepository
) : ViewModel() {
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    private val _selectedCategory = MutableStateFlow<String?>(null)
    val selectedCategory: StateFlow<String?> = _selectedCategory.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // Books flows
    val allBooks: Flow<List<Book>> = bookRepository.getAllBooks()
    val favoriteBooks: Flow<List<Book>> = bookRepository.getFavoriteBooks()
    val downloadedBooks: Flow<List<Book>> = bookRepository.getDownloadedBooks()
    val currentlyReadingBooks: Flow<List<Book>> = bookRepository.getCurrentlyReadingBooks()
    val completedBooks: Flow<List<Book>> = bookRepository.getCompletedBooks()
    
    // Categories
    val allCategories: Flow<List<Category>> = bookRepository.getAllCategories()
    val popularCategories: Flow<List<Category>> = bookRepository.getPopularCategories()
    
    // Search results
    val searchResults: Flow<List<Book>> = searchQuery
        .debounce(300)
        .distinctUntilChanged()
        .flatMapLatest { query ->
            if (query.isBlank()) {
                flowOf(emptyList())
            } else {
                bookRepository.searchBooks(query)
            }
        }
    
    // Books by selected category
    val booksByCategory: Flow<List<Book>> = selectedCategory
        .flatMapLatest { categoryId ->
            if (categoryId != null) {
                bookRepository.getBooksByCategory(categoryId)
            } else {
                flowOf(emptyList())
            }
        }
    
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    fun selectCategory(categoryId: String?) {
        _selectedCategory.value = categoryId
    }
    
    fun getBookById(bookId: String): Flow<Book?> {
        return bookRepository.getBookById(bookId)
    }
    
    fun toggleFavorite(bookId: String, isFavorite: Boolean) {
        viewModelScope.launch {
            try {
                bookRepository.toggleFavorite(bookId, isFavorite)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun updateReadingProgress(bookId: String, page: Int, progress: Float) {
        viewModelScope.launch {
            try {
                bookRepository.updateReadingProgress(bookId, page, progress)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun markAsDownloaded(bookId: String, downloadPath: String) {
        viewModelScope.launch {
            try {
                bookRepository.updateDownloadStatus(bookId, true, downloadPath)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun removeDownload(bookId: String) {
        viewModelScope.launch {
            try {
                bookRepository.updateDownloadStatus(bookId, false, null)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun loadSampleBooks() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val sampleBooks = getSampleBooks()
                bookRepository.insertBooks(sampleBooks)
            } catch (e: Exception) {
                // Handle error
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    private fun getSampleBooks(): List<Book> {
        return listOf(
            Book(
                id = "1",
                title = "مئة عام من العزلة",
                author = "غابرييل غارسيا ماركيز",
                description = "رواية من أعظم الروايات في الأدب العالمي، تحكي قصة عائلة بوينديا عبر مئة عام",
                coverImageUrl = "https://example.com/cover1.jpg",
                fileUrl = "https://example.com/book1.pdf",
                fileType = com.iqraa.library.data.model.FileType.PDF,
                categoryId = "2",
                categoryName = "الروايات",
                publishDate = "1967",
                pageCount = 417,
                fileSize = 2048000,
                rating = 4.5f,
                reviewCount = 1250
            ),
            Book(
                id = "2",
                title = "الأسود يليق بك",
                author = "أحلام مستغانمي",
                description = "رواية عربية معاصرة تتناول قضايا الحب والهوية",
                coverImageUrl = "https://example.com/cover2.jpg",
                fileUrl = "https://example.com/book2.epub",
                fileType = com.iqraa.library.data.model.FileType.EPUB,
                categoryId = "2",
                categoryName = "الروايات",
                publishDate = "2012",
                pageCount = 320,
                fileSize = 1536000,
                rating = 4.2f,
                reviewCount = 890
            ),
            Book(
                id = "3",
                title = "تاريخ الطبري",
                author = "محمد بن جرير الطبري",
                description = "كتاب تاريخي شامل يغطي تاريخ الأمم والملوك",
                coverImageUrl = "https://example.com/cover3.jpg",
                fileUrl = "https://example.com/book3.pdf",
                fileType = com.iqraa.library.data.model.FileType.PDF,
                categoryId = "4",
                categoryName = "التاريخ",
                publishDate = "915",
                pageCount = 2400,
                fileSize = 15360000,
                rating = 4.8f,
                reviewCount = 456
            )
        )
    }
}
