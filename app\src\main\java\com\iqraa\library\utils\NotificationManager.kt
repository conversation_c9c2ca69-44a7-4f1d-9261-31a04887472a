package com.iqraa.library.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.iqraa.library.MainActivity
import com.iqraa.library.R
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val notificationManager = NotificationManagerCompat.from(context)

    init {
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Main notification channel
            val mainChannel = NotificationChannel(
                Constants.NOTIFICATION_CHANNEL_ID,
                Constants.NOTIFICATION_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = Constants.NOTIFICATION_CHANNEL_DESCRIPTION
                enableLights(true)
                enableVibration(true)
            }

            // Download notification channel
            val downloadChannel = NotificationChannel(
                "download_channel",
                "التحميلات",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "إشعارات تحميل الكتب"
                enableLights(false)
                enableVibration(false)
            }

            // Reading reminder channel
            val reminderChannel = NotificationChannel(
                "reminder_channel",
                "تذكيرات القراءة",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "تذكيرات للقراءة اليومية"
                enableLights(true)
                enableVibration(true)
            }

            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            systemNotificationManager.createNotificationChannel(mainChannel)
            systemNotificationManager.createNotificationChannel(downloadChannel)
            systemNotificationManager.createNotificationChannel(reminderChannel)
        }
    }

    fun showDownloadStartedNotification(bookTitle: String, notificationId: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, "download_channel")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setContentTitle("بدء تحميل الكتاب")
            .setContentText("جاري تحميل: $bookTitle")
            .setProgress(100, 0, true)
            .setOngoing(true)
            .setContentIntent(pendingIntent)
            .setAutoCancel(false)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    fun updateDownloadProgressNotification(
        bookTitle: String,
        progress: Int,
        notificationId: Int
    ) {
        val notification = NotificationCompat.Builder(context, "download_channel")
            .setSmallIcon(android.R.drawable.stat_sys_download)
            .setContentTitle("تحميل الكتاب")
            .setContentText("$bookTitle - $progress%")
            .setProgress(100, progress, false)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    fun showDownloadCompletedNotification(bookTitle: String, notificationId: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, "download_channel")
            .setSmallIcon(android.R.drawable.stat_sys_download_done)
            .setContentTitle("اكتمل التحميل")
            .setContentText("تم تحميل: $bookTitle")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setOngoing(false)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    fun showDownloadFailedNotification(bookTitle: String, notificationId: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, "download_channel")
            .setSmallIcon(android.R.drawable.stat_notify_error)
            .setContentTitle("فشل التحميل")
            .setContentText("فشل في تحميل: $bookTitle")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setOngoing(false)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    fun showReadingReminderNotification() {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, "reminder_channel")
            .setSmallIcon(android.R.drawable.ic_menu_agenda)
            .setContentTitle("تذكير القراءة")
            .setContentText("حان وقت القراءة! تابع رحلتك مع الكتب")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(READING_REMINDER_ID, notification)
    }

    fun showNewBookNotification(bookTitle: String, author: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, Constants.NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_menu_add)
            .setContentTitle("كتاب جديد متاح")
            .setContentText("$bookTitle - $author")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(NEW_BOOK_ID, notification)
    }

    fun showReadingGoalAchievedNotification(goalType: String, count: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, Constants.NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(android.R.drawable.btn_star_big_on)
            .setContentTitle("تهانينا! تم تحقيق الهدف")
            .setContentText("لقد حققت هدف $goalType: $count")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()

        notificationManager.notify(GOAL_ACHIEVED_ID, notification)
    }

    fun showReadingStreakNotification(streakDays: Int) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, Constants.NOTIFICATION_CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_menu_today)
            .setContentTitle("سلسلة قراءة رائعة!")
            .setContentText("لقد قرأت لمدة $streakDays أيام متتالية")
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(STREAK_ID, notification)
    }

    fun cancelNotification(notificationId: Int) {
        notificationManager.cancel(notificationId)
    }

    fun cancelAllNotifications() {
        notificationManager.cancelAll()
    }

    companion object {
        const val READING_REMINDER_ID = 1001
        const val NEW_BOOK_ID = 1002
        const val GOAL_ACHIEVED_ID = 1003
        const val STREAK_ID = 1004
        const val DOWNLOAD_BASE_ID = 2000 // Use DOWNLOAD_BASE_ID + bookId.hashCode()
    }
}
