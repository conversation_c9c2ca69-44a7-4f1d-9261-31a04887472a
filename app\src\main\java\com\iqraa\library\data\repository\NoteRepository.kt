package com.iqraa.library.data.repository

import com.iqraa.library.data.database.NoteDao
import com.iqraa.library.data.model.Note
import com.iqraa.library.data.model.Quote
import com.iqraa.library.data.model.NoteType
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NoteRepository @Inject constructor(
    private val noteDao: NoteDao
) {
    
    fun getNotesForBook(bookId: String, userId: String): Flow<List<Note>> = 
        noteDao.getNotesForBook(bookId, userId)
    
    fun getNotesByType(bookId: String, userId: String, noteType: NoteType): Flow<List<Note>> = 
        noteDao.getNotesByType(bookId, userId, noteType)
    
    fun getAllUserNotes(userId: String): Flow<List<Note>> = 
        noteDao.getAllUserNotes(userId)
    
    suspend fun getNoteById(noteId: String): Note? = 
        noteDao.getNoteById(noteId)
    
    suspend fun insertNote(note: Note) = noteDao.insertNote(note)
    
    suspend fun updateNote(note: Note) = noteDao.updateNote(note)
    
    suspend fun deleteNote(note: Note) = noteDao.deleteNote(note)
    
    suspend fun deleteAllNotesForBook(bookId: String, userId: String) = 
        noteDao.deleteAllNotesForBook(bookId, userId)
    
    // Quotes
    fun getUserQuotes(userId: String): Flow<List<Quote>> = 
        noteDao.getUserQuotes(userId)
    
    fun getQuotesForBook(bookId: String): Flow<List<Quote>> = 
        noteDao.getQuotesForBook(bookId)
    
    fun getPublicQuotes(): Flow<List<Quote>> = 
        noteDao.getPublicQuotes()
    
    suspend fun insertQuote(quote: Quote) = noteDao.insertQuote(quote)
    
    suspend fun updateQuote(quote: Quote) = noteDao.updateQuote(quote)
    
    suspend fun deleteQuote(quote: Quote) = noteDao.deleteQuote(quote)
    
    suspend fun incrementShareCount(quoteId: String) = 
        noteDao.incrementShareCount(quoteId)
    
    suspend fun incrementLikesCount(quoteId: String) = 
        noteDao.incrementLikesCount(quoteId)
}
