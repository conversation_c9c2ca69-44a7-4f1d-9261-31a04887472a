<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.IqraaLibrary" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/arabic_blue</item>
        <item name="colorPrimaryVariant">@color/arabic_blue_dark</item>
        <item name="colorSecondary">@color/arabic_gold</item>
        <item name="colorSecondaryVariant">@color/arabic_gold_dark</item>
        <item name="android:statusBarColor">@color/arabic_blue</item>
        <item name="android:navigationBarColor">@color/arabic_blue</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    
    <style name="Theme.IqraaLibrary.Reader" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/arabic_blue</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>
</resources>
