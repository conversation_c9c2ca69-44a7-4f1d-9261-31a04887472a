# مكتبة إقرأ الإلكترونية - Iqraa Digital Library

تطبيق مكتبة إلكترونية شامل لنظام Android يدعم اللغة العربية بالكامل مع واجهة RTL وميزات قراءة متقدمة.

## 🌟 الميزات الرئيسية

### واجهة المستخدم
- ✅ تصميم متجاوب مع جميع أحجام الشاشات
- ✅ دعم كامل للواجهة من اليمين لليسار (RTL)
- ✅ سمة فاتحة وداكنة (Dark/Light Mode)
- ✅ خطوط عربية واضحة وجذابة (أميري، نوتو، القاهرة)
- ✅ Material Design 3

### إدارة الكتب
- ✅ تصفح الكتب حسب التصنيفات (أدب، علم، دين، روايات...)
- ✅ بحث متقدم مع دلالة البحث باللغة العربية
- ✅ إمكانية تنزيل الكتب للقراءة دون اتصال
- ✅ تنظيم الكتب في أرفف افتراضية (مقروءة، جاري القراءة، المفضلة)
- ✅ دعم صيغ متعددة (PDF, EPUB, MOBI, TXT)

### ميزات القراءة
- ✅ عارض كتب بخصائص متقدمة
- ✅ تعديل حجم الخط ونوع الخط
- ✅ تغيير لون الخلفية وتباعد الأسطر
- ✅ إضافة علامات وتظليل النصوص
- ✅ تدوين ملاحظات على الهوامش
- ✅ حفظ آخر صفحة تم الوصول إليها تلقائياً
- ✅ شريط تقدم القراءة

### المستخدمين والمجتمع
- ✅ نظام تسجيل دخول/حساب مستخدم
- ✅ تقييم الكتب وكتابة مراجعات
- ✅ مشاركة اقتباسات
- ✅ نظام توصيات كتب بناءً على القراءات السابقة
- ✅ إحصائيات القراءة الشخصية

## 🏗️ البنية التقنية

### التقنيات المستخدمة
- **Kotlin** - لغة البرمجة الأساسية
- **Jetpack Compose** - واجهة المستخدم الحديثة
- **Room Database** - قاعدة البيانات المحلية
- **Hilt** - حقن التبعيات
- **MVVM Architecture** - نمط العمارة
- **Coroutines & Flow** - البرمجة غير المتزامنة
- **Navigation Component** - التنقل بين الشاشات
- **Material Design 3** - تصميم المواد

### مكونات التطبيق

#### نماذج البيانات (Models)
- `Book` - نموذج الكتاب
- `User` - نموذج المستخدم
- `Category` - نموذج التصنيف
- `Review` - نموذج المراجعة
- `Note` - نموذج الملاحظات
- `Quote` - نموذج الاقتباسات

#### قاعدة البيانات (Database)
- `AppDatabase` - قاعدة البيانات الرئيسية
- `BookDao` - واجهة الوصول لبيانات الكتب
- `UserDao` - واجهة الوصول لبيانات المستخدمين
- `CategoryDao` - واجهة الوصول لبيانات التصنيفات
- `ReviewDao` - واجهة الوصول لبيانات المراجعات
- `NoteDao` - واجهة الوصول لبيانات الملاحظات

#### المستودعات (Repositories)
- `BookRepository` - مستودع إدارة الكتب
- `UserRepository` - مستودع إدارة المستخدمين

#### نماذج العرض (ViewModels)
- `BookViewModel` - إدارة حالة الكتب
- `UserViewModel` - إدارة حالة المستخدم

#### الشاشات (Screens)
- `HomeScreen` - الشاشة الرئيسية
- `LibraryScreen` - شاشة المكتبة الشخصية
- `SearchScreen` - شاشة البحث
- `CategoriesScreen` - شاشة التصنيفات
- `BookDetailScreen` - شاشة تفاصيل الكتاب
- `ReaderScreen` - شاشة القراءة
- `ProfileScreen` - شاشة الملف الشخصي
- `SettingsScreen` - شاشة الإعدادات

## 🚀 التثبيت والتشغيل

### المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK 24 أو أحدث
- Kotlin 1.9.10 أو أحدث

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/iqraa-library.git
cd iqraa-library
```

2. **فتح المشروع في Android Studio**
```bash
# افتح Android Studio واختر "Open an existing project"
# ثم اختر مجلد المشروع
```

3. **تحميل الخطوط العربية**
```bash
# قم بتحميل الخطوط التالية ووضعها في app/src/main/res/font/
# - amiri_regular.ttf
# - amiri_bold.ttf  
# - noto_sans_arabic_regular.ttf
# - noto_sans_arabic_bold.ttf
# - cairo_regular.ttf
# - cairo_bold.ttf
```

4. **بناء المشروع**
```bash
./gradlew build
```

5. **تشغيل التطبيق**
```bash
./gradlew installDebug
```

## 📱 لقطات الشاشة

### الشاشة الرئيسية
- عرض الكتب المضافة حديثاً
- التصنيفات الشائعة
- إحصائيات القراءة الشخصية

### شاشة المكتبة
- الكتب المحملة
- الكتب المفضلة
- الكتب قيد القراءة
- الكتب المكتملة

### شاشة القراءة
- واجهة قراءة نظيفة ومريحة
- إعدادات الخط والألوان
- إضافة الملاحظات والعلامات
- شريط التقدم

## 🔧 التخصيص والتطوير

### إضافة كتب جديدة
```kotlin
val newBook = Book(
    id = "unique_id",
    title = "عنوان الكتاب",
    author = "اسم المؤلف",
    description = "وصف الكتاب",
    coverImageUrl = "رابط صورة الغلاف",
    fileUrl = "رابط ملف الكتاب",
    fileType = FileType.PDF,
    categoryId = "category_id",
    categoryName = "اسم التصنيف",
    // ... باقي الخصائص
)

bookRepository.insertBook(newBook)
```

### إضافة تصنيفات جديدة
```kotlin
val newCategory = Category(
    id = "unique_id",
    name = "اسم التصنيف",
    nameEn = "Category Name",
    description = "وصف التصنيف",
    color = "#2196F3"
)

categoryRepository.insertCategory(newCategory)
```

### تخصيص الألوان والخطوط
```kotlin
// في ملف Color.kt
val CustomPrimary = Color(0xFF1E3A8A)
val CustomSecondary = Color(0xFFD4AF37)

// في ملف Type.kt
val CustomFont = FontFamily(
    Font(R.font.custom_regular, FontWeight.Normal),
    Font(R.font.custom_bold, FontWeight.Bold)
)
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
./gradlew test

# اختبارات الواجهة
./gradlew connectedAndroidTest
```

### إضافة اختبارات جديدة
```kotlin
@Test
fun testBookInsertion() {
    // اختبار إدراج كتاب جديد
    val book = createSampleBook()
    bookRepository.insertBook(book)
    
    val retrievedBook = bookRepository.getBookById(book.id)
    assertEquals(book.title, retrievedBook?.title)
}
```

## 📚 التوثيق الإضافي

### هيكل المجلدات
```
app/
├── src/main/java/com/iqraa/library/
│   ├── data/
│   │   ├── database/     # قاعدة البيانات والـ DAOs
│   │   ├── model/        # نماذج البيانات
│   │   └── repository/   # المستودعات
│   ├── di/              # حقن التبعيات
│   ├── ui/
│   │   ├── components/   # المكونات المشتركة
│   │   ├── navigation/   # نظام التنقل
│   │   ├── screens/      # الشاشات
│   │   ├── theme/        # الألوان والخطوط
│   │   └── viewmodel/    # نماذج العرض
│   └── MainActivity.kt
└── src/main/res/
    ├── font/            # ملفات الخطوط
    ├── values/          # الألوان والنصوص
    └── xml/             # ملفات التكوين
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://iqraa-library.com
- تويتر: [@IqraaLibrary](https://twitter.com/IqraaLibrary)

## 🙏 شكر وتقدير

- فريق Material Design لتوفير مكونات التصميم
- مطوري Jetpack Compose لتسهيل بناء الواجهات
- مجتمع المطورين العرب للدعم والمساعدة

---

**مكتبة إقرأ** - اقرأ، تعلم، وشارك المعرفة 📚✨
