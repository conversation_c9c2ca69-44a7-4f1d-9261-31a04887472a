package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.components.ReviewCard
import com.iqraa.library.ui.viewmodel.ReviewViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReviewsScreen(
    bookId: String,
    onBackClick: () -> Unit,
    onWriteReviewClick: () -> Unit,
    reviewViewModel: ReviewViewModel = hiltViewModel()
) {
    val reviews by reviewViewModel.getReviewsForBook(bookId).collectAsState(initial = emptyList())
    val isLoading by reviewViewModel.isLoading.collectAsState()
    
    var averageRating by remember { mutableStateOf(0f) }
    var reviewCount by remember { mutableStateOf(0) }
    
    LaunchedEffect(bookId) {
        averageRating = reviewViewModel.getAverageRatingForBook(bookId)
        reviewCount = reviewViewModel.getReviewCountForBook(bookId)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text("المراجعات") },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            },
            actions = {
                IconButton(onClick = onWriteReviewClick) {
                    Icon(
                        imageVector = Icons.Default.RateReview,
                        contentDescription = stringResource(R.string.write_review)
                    )
                }
            }
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Reviews Summary
                item {
                    ReviewsSummaryCard(
                        averageRating = averageRating,
                        reviewCount = reviewCount,
                        onWriteReviewClick = onWriteReviewClick
                    )
                }
                
                // Reviews List
                if (reviews.isEmpty()) {
                    item {
                        EmptyReviewsCard(onWriteReviewClick = onWriteReviewClick)
                    }
                } else {
                    items(reviews) { review ->
                        ReviewCard(
                            review = review,
                            onLikeClick = { /* Handle like */ },
                            onReportClick = { /* Handle report */ }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ReviewsSummaryCard(
    averageRating: Float,
    reviewCount: Int,
    onWriteReviewClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "التقييم العام",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = String.format("%.1f", averageRating),
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Row {
                            repeat(5) { index ->
                                Icon(
                                    imageVector = if (index < averageRating.toInt()) {
                                        Icons.Default.Star
                                    } else {
                                        Icons.Default.StarBorder
                                    },
                                    contentDescription = null,
                                    tint = if (index < averageRating.toInt()) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    },
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                    }
                    
                    Text(
                        text = "$reviewCount مراجعة",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Button(
                    onClick = onWriteReviewClick
                ) {
                    Icon(
                        imageVector = Icons.Default.RateReview,
                        contentDescription = null
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("اكتب مراجعة")
                }
            }
        }
    }
}

@Composable
private fun EmptyReviewsCard(
    onWriteReviewClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.RateReview,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "لا توجد مراجعات بعد",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "كن أول من يكتب مراجعة لهذا الكتاب",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onWriteReviewClick
            ) {
                Text("اكتب أول مراجعة")
            }
        }
    }
}
