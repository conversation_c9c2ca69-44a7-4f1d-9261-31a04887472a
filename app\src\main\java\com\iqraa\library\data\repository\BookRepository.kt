package com.iqraa.library.data.repository

import com.iqraa.library.data.database.BookDao
import com.iqraa.library.data.database.CategoryDao
import com.iqraa.library.data.model.Book
import com.iqraa.library.data.model.Category
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BookRepository @Inject constructor(
    private val bookDao: BookDao,
    private val categoryDao: CategoryDao
) {
    
    fun getAllBooks(): Flow<List<Book>> = bookDao.getAllBooks()
    
    fun getBooksByCategory(categoryId: String): Flow<List<Book>> = 
        bookDao.getBooksByCategory(categoryId)
    
    fun getFavoriteBooks(): Flow<List<Book>> = bookDao.getFavoriteBooks()
    
    fun getDownloadedBooks(): Flow<List<Book>> = bookDao.getDownloadedBooks()
    
    fun getCurrentlyReadingBooks(): Flow<List<Book>> = bookDao.getCurrentlyReadingBooks()
    
    fun getCompletedBooks(): Flow<List<Book>> = bookDao.getCompletedBooks()
    
    fun searchBooks(query: String): Flow<List<Book>> = bookDao.searchBooks(query)
    
    fun getBookById(bookId: String): Flow<Book?> = bookDao.getBookByIdFlow(bookId)
    
    suspend fun insertBook(book: Book) {
        bookDao.insertBook(book)
        categoryDao.incrementBookCount(book.categoryId)
    }
    
    suspend fun insertBooks(books: List<Book>) {
        bookDao.insertBooks(books)
        books.forEach { book ->
            categoryDao.incrementBookCount(book.categoryId)
        }
    }
    
    suspend fun updateBook(book: Book) = bookDao.updateBook(book)
    
    suspend fun deleteBook(book: Book) {
        bookDao.deleteBook(book)
        categoryDao.decrementBookCount(book.categoryId)
    }
    
    suspend fun toggleFavorite(bookId: String, isFavorite: Boolean) = 
        bookDao.updateFavoriteStatus(bookId, isFavorite)
    
    suspend fun updateDownloadStatus(bookId: String, isDownloaded: Boolean, downloadPath: String?) = 
        bookDao.updateDownloadStatus(bookId, isDownloaded, downloadPath)
    
    suspend fun updateReadingProgress(bookId: String, page: Int, progress: Float) = 
        bookDao.updateReadingProgress(bookId, page, progress)
    
    // Categories
    fun getAllCategories(): Flow<List<Category>> = categoryDao.getAllCategories()
    
    fun getPopularCategories(): Flow<List<Category>> = categoryDao.getPopularCategories()
    
    fun getCategoryById(categoryId: String): Flow<Category?> = categoryDao.getCategoryByIdFlow(categoryId)
    
    suspend fun insertCategories(categories: List<Category>) = categoryDao.insertCategories(categories)
    
    // Statistics
    suspend fun getBooksCount(): Int = bookDao.getBooksCount()
    
    suspend fun getDownloadedBooksCount(): Int = bookDao.getDownloadedBooksCount()
}
