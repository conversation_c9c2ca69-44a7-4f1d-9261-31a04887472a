package com.iqraa.library.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.iqraa.library.data.model.User
import com.iqraa.library.data.model.UserPreferences
import com.iqraa.library.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _currentUserId = MutableStateFlow("default_user") // For demo purposes
    val currentUserId: StateFlow<String> = _currentUserId.asStateFlow()
    
    val currentUser: Flow<User?> = currentUserId.flatMapLatest { userId ->
        userRepository.getUserByIdFlow(userId)
    }
    
    val userPreferences: Flow<UserPreferences?> = currentUserId.flatMapLatest { userId ->
        userRepository.getUserPreferencesFlow(userId)
    }
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    init {
        // Initialize default user and preferences
        viewModelScope.launch {
            initializeDefaultUser()
        }
    }
    
    private suspend fun initializeDefaultUser() {
        val userId = _currentUserId.value
        
        // Check if user exists
        val existingUser = userRepository.getUserById(userId)
        if (existingUser == null) {
            // Create default user
            val defaultUser = User(
                id = userId,
                username = "مستخدم جديد",
                email = "<EMAIL>",
                displayName = "مستخدم جديد",
                booksRead = 0,
                totalReadingTime = 0,
                favoriteGenres = listOf("الأدب العربي", "الروايات"),
                readingGoal = 12
            )
            userRepository.insertUser(defaultUser)
        }
        
        // Check if preferences exist
        val existingPreferences = userRepository.getUserPreferences(userId)
        if (existingPreferences == null) {
            userRepository.createDefaultPreferences(userId)
        }
    }
    
    fun updateThemeMode(isDarkMode: Boolean) {
        viewModelScope.launch {
            try {
                userRepository.updateThemeMode(_currentUserId.value, isDarkMode)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun updateFontSize(fontSize: Float) {
        viewModelScope.launch {
            try {
                userRepository.updateFontSize(_currentUserId.value, fontSize)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun updateFontFamily(fontFamily: String) {
        viewModelScope.launch {
            try {
                userRepository.updateFontFamily(_currentUserId.value, fontFamily)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun updateUserPreferences(preferences: UserPreferences) {
        viewModelScope.launch {
            try {
                userRepository.updateUserPreferences(preferences)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun updateUser(user: User) {
        viewModelScope.launch {
            try {
                userRepository.updateUser(user)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun incrementBooksRead() {
        viewModelScope.launch {
            try {
                userRepository.incrementBooksRead(_currentUserId.value)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
    
    fun addReadingTime(minutes: Long) {
        viewModelScope.launch {
            try {
                userRepository.addReadingTime(_currentUserId.value, minutes)
            } catch (e: Exception) {
                // Handle error
            }
        }
    }
}
