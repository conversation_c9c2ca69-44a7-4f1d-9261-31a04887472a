package com.iqraa.library.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "reviews")
data class Review(
    @PrimaryKey
    val id: String,
    val bookId: String,
    val userId: String,
    val userName: String,
    val userAvatar: String? = null,
    val rating: Float, // 1-5 stars
    val title: String,
    val content: String,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val likesCount: Int = 0,
    val isVerified: Boolean = false,
    val containsSpoilers: Boolean = false
)

@Entity(tableName = "user_book_ratings")
data class UserBookRating(
    @PrimaryKey
    val id: String,
    val userId: String,
    val bookId: String,
    val rating: Float,
    val createdAt: Long = System.currentTimeMillis()
)
