package com.iqraa.library.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PreferencesManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val preferences: SharedPreferences = context.getSharedPreferences(
        Constants.PREFERENCES_NAME,
        Context.MODE_PRIVATE
    )
    
    // User Settings
    var currentUserId: String
        get() = preferences.getString(Constants.KEY_USER_ID, "default_user") ?: "default_user"
        set(value) = preferences.edit { putString(Constants.KEY_USER_ID, value) }
    
    var isFirstLaunch: Boolean
        get() = preferences.getBoolean(Constants.KEY_FIRST_LAUNCH, true)
        set(value) = preferences.edit { putBoolean(Constants.KEY_FIRST_LAUNCH, value) }
    
    var isDarkMode: Boolean
        get() = preferences.getBoolean(Constants.KEY_THEME_MODE, false)
        set(value) = preferences.edit { putBoolean(Constants.KEY_THEME_MODE, value) }
    
    var fontSize: Float
        get() = preferences.getFloat(Constants.KEY_FONT_SIZE, Constants.DEFAULT_FONT_SIZE)
        set(value) = preferences.edit { putFloat(Constants.KEY_FONT_SIZE, value) }
    
    var fontFamily: String
        get() = preferences.getString(Constants.KEY_FONT_FAMILY, Constants.DEFAULT_FONT_FAMILY) 
            ?: Constants.DEFAULT_FONT_FAMILY
        set(value) = preferences.edit { putString(Constants.KEY_FONT_FAMILY, value) }
    
    var isRTL: Boolean
        get() = preferences.getBoolean(Constants.KEY_RTL_MODE, true)
        set(value) = preferences.edit { putBoolean(Constants.KEY_RTL_MODE, value) }
    
    // Reading Settings
    var lineSpacing: Float
        get() = preferences.getFloat("line_spacing", Constants.DEFAULT_LINE_SPACING)
        set(value) = preferences.edit { putFloat("line_spacing", value) }
    
    var backgroundColor: String
        get() = preferences.getString("background_color", Constants.DEFAULT_BACKGROUND_COLOR) 
            ?: Constants.DEFAULT_BACKGROUND_COLOR
        set(value) = preferences.edit { putString("background_color", value) }
    
    var textColor: String
        get() = preferences.getString("text_color", Constants.DEFAULT_TEXT_COLOR) 
            ?: Constants.DEFAULT_TEXT_COLOR
        set(value) = preferences.edit { putString("text_color", value) }
    
    var autoNightMode: Boolean
        get() = preferences.getBoolean("auto_night_mode", false)
        set(value) = preferences.edit { putBoolean("auto_night_mode", value) }
    
    // Download Settings
    var downloadOnWifiOnly: Boolean
        get() = preferences.getBoolean("download_wifi_only", true)
        set(value) = preferences.edit { putBoolean("download_wifi_only", value) }
    
    var autoDownloadUpdates: Boolean
        get() = preferences.getBoolean("auto_download_updates", false)
        set(value) = preferences.edit { putBoolean("auto_download_updates", value) }
    
    // Notification Settings
    var notificationsEnabled: Boolean
        get() = preferences.getBoolean("notifications_enabled", true)
        set(value) = preferences.edit { putBoolean("notifications_enabled", value) }
    
    var newBooksNotification: Boolean
        get() = preferences.getBoolean("new_books_notification", true)
        set(value) = preferences.edit { putBoolean("new_books_notification", value) }
    
    var readingReminders: Boolean
        get() = preferences.getBoolean("reading_reminders", false)
        set(value) = preferences.edit { putBoolean("reading_reminders", value) }
    
    // Reading Statistics
    var totalReadingTime: Long
        get() = preferences.getLong("total_reading_time", 0L)
        set(value) = preferences.edit { putLong("total_reading_time", value) }
    
    var booksCompleted: Int
        get() = preferences.getInt("books_completed", 0)
        set(value) = preferences.edit { putInt("books_completed", value) }
    
    var currentStreak: Int
        get() = preferences.getInt("current_streak", 0)
        set(value) = preferences.edit { putInt("current_streak", value) }
    
    var longestStreak: Int
        get() = preferences.getInt("longest_streak", 0)
        set(value) = preferences.edit { putInt("longest_streak", value) }
    
    var lastReadingDate: Long
        get() = preferences.getLong("last_reading_date", 0L)
        set(value) = preferences.edit { putLong("last_reading_date", value) }
    
    // App Settings
    var appVersion: String
        get() = preferences.getString("app_version", Constants.APP_VERSION) ?: Constants.APP_VERSION
        set(value) = preferences.edit { putString("app_version", value) }
    
    var lastSyncTime: Long
        get() = preferences.getLong("last_sync_time", 0L)
        set(value) = preferences.edit { putLong("last_sync_time", value) }
    
    var analyticsEnabled: Boolean
        get() = preferences.getBoolean("analytics_enabled", true)
        set(value) = preferences.edit { putBoolean("analytics_enabled", value) }
    
    // Search History
    fun addSearchQuery(query: String) {
        val searchHistory = getSearchHistory().toMutableList()
        searchHistory.remove(query) // Remove if already exists
        searchHistory.add(0, query) // Add to beginning
        
        // Keep only last 10 searches
        if (searchHistory.size > 10) {
            searchHistory.removeAt(searchHistory.size - 1)
        }
        
        val searchHistoryString = searchHistory.joinToString(",")
        preferences.edit { putString("search_history", searchHistoryString) }
    }
    
    fun getSearchHistory(): List<String> {
        val searchHistoryString = preferences.getString("search_history", "") ?: ""
        return if (searchHistoryString.isBlank()) {
            emptyList()
        } else {
            searchHistoryString.split(",")
        }
    }
    
    fun clearSearchHistory() {
        preferences.edit { remove("search_history") }
    }
    
    // Recently Viewed Books
    fun addRecentlyViewedBook(bookId: String) {
        val recentBooks = getRecentlyViewedBooks().toMutableList()
        recentBooks.remove(bookId) // Remove if already exists
        recentBooks.add(0, bookId) // Add to beginning
        
        // Keep only last 20 books
        if (recentBooks.size > 20) {
            recentBooks.removeAt(recentBooks.size - 1)
        }
        
        val recentBooksString = recentBooks.joinToString(",")
        preferences.edit { putString("recently_viewed_books", recentBooksString) }
    }
    
    fun getRecentlyViewedBooks(): List<String> {
        val recentBooksString = preferences.getString("recently_viewed_books", "") ?: ""
        return if (recentBooksString.isBlank()) {
            emptyList()
        } else {
            recentBooksString.split(",")
        }
    }
    
    // Reading Goals
    var yearlyReadingGoal: Int
        get() = preferences.getInt("yearly_reading_goal", 12)
        set(value) = preferences.edit { putInt("yearly_reading_goal", value) }
    
    var monthlyReadingGoal: Int
        get() = preferences.getInt("monthly_reading_goal", 1)
        set(value) = preferences.edit { putInt("monthly_reading_goal", value) }
    
    var dailyReadingGoal: Int // in minutes
        get() = preferences.getInt("daily_reading_goal", 30)
        set(value) = preferences.edit { putInt("daily_reading_goal", value) }
    
    // Clear all preferences
    fun clearAllPreferences() {
        preferences.edit { clear() }
    }
    
    // Export preferences as JSON string
    fun exportPreferences(): String {
        val allPrefs = preferences.all
        val jsonBuilder = StringBuilder()
        jsonBuilder.append("{")
        
        allPrefs.entries.forEachIndexed { index, entry ->
            jsonBuilder.append("\"${entry.key}\":\"${entry.value}\"")
            if (index < allPrefs.size - 1) {
                jsonBuilder.append(",")
            }
        }
        
        jsonBuilder.append("}")
        return jsonBuilder.toString()
    }
    
    // Check if this is a new app version
    fun isNewAppVersion(): Boolean {
        val savedVersion = appVersion
        val currentVersion = Constants.APP_VERSION
        return savedVersion != currentVersion
    }
    
    // Update app version
    fun updateAppVersion() {
        appVersion = Constants.APP_VERSION
    }
}
