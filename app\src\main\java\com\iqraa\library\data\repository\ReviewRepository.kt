package com.iqraa.library.data.repository

import com.iqraa.library.data.database.ReviewDao
import com.iqraa.library.data.model.Review
import com.iqraa.library.data.model.UserBookRating
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReviewRepository @Inject constructor(
    private val reviewDao: ReviewDao
) {
    
    fun getReviewsForBook(bookId: String): Flow<List<Review>> = 
        reviewDao.getReviewsForBook(bookId)
    
    fun getReviewsByUser(userId: String): Flow<List<Review>> = 
        reviewDao.getReviewsByUser(userId)
    
    suspend fun getReviewById(reviewId: String): Review? = 
        reviewDao.getReviewById(reviewId)
    
    suspend fun insertReview(review: Review) = reviewDao.insertReview(review)
    
    suspend fun updateReview(review: Review) = reviewDao.updateReview(review)
    
    suspend fun deleteReview(review: Review) = reviewDao.deleteReview(review)
    
    suspend fun getAverageRatingForBook(bookId: String): Float = 
        reviewDao.getAverageRatingForBook(bookId) ?: 0f
    
    suspend fun getReviewCountForBook(bookId: String): Int = 
        reviewDao.getReviewCountForBook(bookId)
    
    // User Ratings
    suspend fun getUserRatingForBook(userId: String, bookId: String): UserBookRating? = 
        reviewDao.getUserRatingForBook(userId, bookId)
    
    suspend fun insertUserRating(rating: UserBookRating) = 
        reviewDao.insertUserRating(rating)
    
    suspend fun deleteUserRating(userId: String, bookId: String) = 
        reviewDao.deleteUserRating(userId, bookId)
    
    suspend fun getAverageUserRatingForBook(bookId: String): Float = 
        reviewDao.getAverageUserRatingForBook(bookId) ?: 0f
}
