package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit,
    userViewModel: UserViewModel = hiltViewModel()
) {
    val userPreferences by userViewModel.userPreferences.collectAsState(initial = null)

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text(stringResource(R.string.settings)) },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            }
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Appearance Section
            item {
                SettingsSectionHeader(stringResource(R.string.appearance))
            }
            
            item {
                SettingsCard {
                    Column {
                        // Dark Mode Toggle
                        SettingsToggleItem(
                            icon = Icons.Default.DarkMode,
                            title = stringResource(R.string.dark_mode),
                            checked = userPreferences?.isDarkMode ?: false,
                            onCheckedChange = { userViewModel.updateThemeMode(it) }
                        )
                        
                        Divider()
                        
                        // Font Size
                        SettingsSliderItem(
                            icon = Icons.Default.FormatSize,
                            title = stringResource(R.string.font_size),
                            value = userPreferences?.fontSize ?: 16f,
                            valueRange = 12f..24f,
                            onValueChange = { userViewModel.updateFontSize(it) },
                            valueFormatter = { "${it.toInt()}sp" }
                        )
                        
                        Divider()
                        
                        // Font Family
                        SettingsDropdownItem(
                            icon = Icons.Default.FontDownload,
                            title = stringResource(R.string.font_family),
                            selectedValue = userPreferences?.fontFamily ?: "Amiri",
                            options = listOf("Amiri", "Noto Sans Arabic", "Cairo"),
                            onValueChange = { userViewModel.updateFontFamily(it) }
                        )
                    }
                }
            }
            
            // Reading Section
            item {
                SettingsSectionHeader("إعدادات القراءة")
            }
            
            item {
                SettingsCard {
                    Column {
                        SettingsToggleItem(
                            icon = Icons.Default.AutoMode,
                            title = "الوضع الليلي التلقائي",
                            checked = userPreferences?.autoNightMode ?: false,
                            onCheckedChange = { 
                                userPreferences?.let { prefs ->
                                    userViewModel.updateUserPreferences(
                                        prefs.copy(autoNightMode = it)
                                    )
                                }
                            }
                        )
                        
                        Divider()
                        
                        SettingsSliderItem(
                            icon = Icons.Default.FormatLineSpacing,
                            title = stringResource(R.string.line_spacing),
                            value = userPreferences?.lineSpacing ?: 1.5f,
                            valueRange = 1.0f..2.5f,
                            onValueChange = { 
                                userPreferences?.let { prefs ->
                                    userViewModel.updateUserPreferences(
                                        prefs.copy(lineSpacing = it)
                                    )
                                }
                            },
                            valueFormatter = { String.format("%.1f", it) }
                        )
                    }
                }
            }
            
            // Download Section
            item {
                SettingsSectionHeader(stringResource(R.string.download_settings))
            }
            
            item {
                SettingsCard {
                    SettingsToggleItem(
                        icon = Icons.Default.Wifi,
                        title = stringResource(R.string.wifi_only),
                        subtitle = "تحميل الكتب عبر الواي فاي فقط",
                        checked = userPreferences?.downloadOnWifiOnly ?: true,
                        onCheckedChange = { 
                            userPreferences?.let { prefs ->
                                userViewModel.updateUserPreferences(
                                    prefs.copy(downloadOnWifiOnly = it)
                                )
                            }
                        }
                    )
                }
            }
            
            // Notifications Section
            item {
                SettingsSectionHeader(stringResource(R.string.notifications_settings))
            }
            
            item {
                SettingsCard {
                    SettingsToggleItem(
                        icon = Icons.Default.Notifications,
                        title = "تفعيل الإشعارات",
                        subtitle = "تلقي إشعارات حول الكتب الجديدة والتحديثات",
                        checked = userPreferences?.notificationsEnabled ?: true,
                        onCheckedChange = { 
                            userPreferences?.let { prefs ->
                                userViewModel.updateUserPreferences(
                                    prefs.copy(notificationsEnabled = it)
                                )
                            }
                        }
                    )
                }
            }
            
            // About Section
            item {
                SettingsSectionHeader(stringResource(R.string.about))
            }
            
            item {
                SettingsCard {
                    Column {
                        SettingsClickableItem(
                            icon = Icons.Default.Info,
                            title = "حول التطبيق",
                            subtitle = "الإصدار 1.0.0",
                            onClick = { /* Handle about */ }
                        )
                        
                        Divider()
                        
                        SettingsClickableItem(
                            icon = Icons.Default.PrivacyTip,
                            title = stringResource(R.string.privacy_policy),
                            onClick = { /* Handle privacy policy */ }
                        )
                        
                        Divider()
                        
                        SettingsClickableItem(
                            icon = Icons.Default.Description,
                            title = stringResource(R.string.terms_of_service),
                            onClick = { /* Handle terms */ }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingsSectionHeader(title: String) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        color = MaterialTheme.colorScheme.primary,
        modifier = Modifier.padding(vertical = 8.dp)
    )
}

@Composable
private fun SettingsCard(
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        content()
    }
}

@Composable
private fun SettingsToggleItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String? = null,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

@Composable
private fun SettingsSliderItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit,
    valueFormatter: (Float) -> String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f)
            )
            
            Text(
                text = valueFormatter(value),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            modifier = Modifier.padding(start = 40.dp)
        )
    }
}

@Composable
private fun SettingsDropdownItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    selectedValue: String,
    options: List<String>,
    onValueChange: (String) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            
            Box {
                OutlinedButton(
                    onClick = { expanded = true },
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(selectedValue)
                    Spacer(modifier = Modifier.width(8.dp))
                    Icon(
                        imageVector = Icons.Default.ArrowDropDown,
                        contentDescription = null
                    )
                }
                
                DropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    options.forEach { option ->
                        DropdownMenuItem(
                            text = { Text(option) },
                            onClick = {
                                onValueChange(option)
                                expanded = false
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingsClickableItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String? = null,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge
            )
            
            if (subtitle != null) {
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        IconButton(onClick = onClick) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null
            )
        }
    }
}
