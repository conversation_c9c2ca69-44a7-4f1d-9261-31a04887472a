package com.iqraa.library.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.iqraa.library.data.model.Note
import com.iqraa.library.data.model.Quote
import com.iqraa.library.data.model.NoteType
import com.iqraa.library.data.repository.NoteRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class NoteViewModel @Inject constructor(
    private val noteRepository: NoteRepository
) : ViewModel() {
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    fun getNotesForBook(bookId: String, userId: String): Flow<List<Note>> = 
        noteRepository.getNotesForBook(bookId, userId)
    
    fun getBookmarks(bookId: String, userId: String): Flow<List<Note>> = 
        noteRepository.getNotesByType(bookId, userId, NoteType.BOOKMARK)
    
    fun getHighlights(bookId: String, userId: String): Flow<List<Note>> = 
        noteRepository.getNotesByType(bookId, userId, NoteType.HIGHLIGHT)
    
    fun getAllUserNotes(userId: String): Flow<List<Note>> = 
        noteRepository.getAllUserNotes(userId)
    
    fun getUserQuotes(userId: String): Flow<List<Quote>> = 
        noteRepository.getUserQuotes(userId)
    
    fun getPublicQuotes(): Flow<List<Quote>> = 
        noteRepository.getPublicQuotes()
    
    fun addNote(
        bookId: String,
        userId: String,
        pageNumber: Int,
        content: String,
        selectedText: String? = null,
        noteType: NoteType = NoteType.NOTE,
        highlightColor: String = "#FFFF00"
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val note = Note(
                    id = UUID.randomUUID().toString(),
                    bookId = bookId,
                    userId = userId,
                    pageNumber = pageNumber,
                    content = content,
                    selectedText = selectedText,
                    highlightColor = highlightColor,
                    noteType = noteType
                )
                
                noteRepository.insertNote(note)
            } catch (e: Exception) {
                _error.value = "فشل في إضافة الملاحظة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun addBookmark(
        bookId: String,
        userId: String,
        pageNumber: Int,
        title: String = "علامة مرجعية"
    ) {
        addNote(
            bookId = bookId,
            userId = userId,
            pageNumber = pageNumber,
            content = title,
            noteType = NoteType.BOOKMARK
        )
    }
    
    fun addHighlight(
        bookId: String,
        userId: String,
        pageNumber: Int,
        selectedText: String,
        highlightColor: String = "#FFFF00"
    ) {
        addNote(
            bookId = bookId,
            userId = userId,
            pageNumber = pageNumber,
            content = "",
            selectedText = selectedText,
            noteType = NoteType.HIGHLIGHT,
            highlightColor = highlightColor
        )
    }
    
    fun updateNote(note: Note) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val updatedNote = note.copy(
                    updatedAt = System.currentTimeMillis()
                )
                noteRepository.updateNote(updatedNote)
            } catch (e: Exception) {
                _error.value = "فشل في تحديث الملاحظة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteNote(note: Note) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                noteRepository.deleteNote(note)
            } catch (e: Exception) {
                _error.value = "فشل في حذف الملاحظة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun createQuote(
        bookId: String,
        bookTitle: String,
        author: String,
        userId: String,
        text: String,
        pageNumber: Int,
        isShared: Boolean = false
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val quote = Quote(
                    id = UUID.randomUUID().toString(),
                    bookId = bookId,
                    bookTitle = bookTitle,
                    author = author,
                    userId = userId,
                    text = text,
                    pageNumber = pageNumber,
                    isShared = isShared
                )
                
                noteRepository.insertQuote(quote)
            } catch (e: Exception) {
                _error.value = "فشل في إنشاء الاقتباس: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun shareQuote(quoteId: String) {
        viewModelScope.launch {
            try {
                noteRepository.incrementShareCount(quoteId)
            } catch (e: Exception) {
                _error.value = "فشل في مشاركة الاقتباس: ${e.message}"
            }
        }
    }
    
    fun likeQuote(quoteId: String) {
        viewModelScope.launch {
            try {
                noteRepository.incrementLikesCount(quoteId)
            } catch (e: Exception) {
                _error.value = "فشل في إعجاب الاقتباس: ${e.message}"
            }
        }
    }
    
    fun clearError() {
        _error.value = null
    }
}
