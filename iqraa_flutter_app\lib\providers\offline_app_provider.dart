import 'package:flutter/material.dart';
import '../models/book.dart';
import '../services/database_service.dart';
import '../models/user.dart';

class OfflineAppProvider with ChangeNotifier {
  // User data
  User _currentUser = User(
    id: '1',
    name: 'محم<PERSON> أحمد',
    email: '<EMAIL>',
    profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    joinDate: DateTime.now().subtract(const Duration(days: 365)),
    booksRead: 25,
    readingGoal: 50,
    favoriteGenres: ['الأدب العربي', 'التاريخ', 'الفلسفة'],
  );

  // Database service
  final DatabaseService _databaseService = DatabaseService();

  // Books data
  List<Book> _allBooks = [];
  List<Book> _searchResults = [];
  String _searchQuery = '';
  String _selectedCategory = 'الكل';
  bool _isLoading = true;

  // Navigation
  int _currentPageIndex = 0;

  // Constructor
  OfflineAppProvider() {
    _initializeApp();
  }

  // Getters
  User get currentUser => _currentUser;
  List<Book> get allBooks => _allBooks;
  List<Book> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  int get currentPageIndex => _currentPageIndex;
  bool get isLoading => _isLoading;

  List<Book> get favoriteBooks {
    return _allBooks.where((book) => book.isFavorite).toList();
  }

  List<Book> get recentBooks {
    final recent = _allBooks.where((book) => book.lastReadDate != null).toList();
    recent.sort((a, b) => b.lastReadDate!.compareTo(a.lastReadDate!));
    return recent.take(5).toList();
  }

  List<Book> get continueReadingBooks {
    return _allBooks
        .where((book) => book.readingProgress > 0 && book.readingProgress < 1.0)
        .toList();
  }

  List<Book> get preloadedBooks {
    return _allBooks.where((book) => book.hasLocalFile).toList();
  }

  List<Book> get islamicBooks {
    return _allBooks.where((book) => book.category.contains('إسلامية')).toList();
  }

  List<Book> get novels {
    return _allBooks.where((book) => book.category.contains('رواية')).toList();
  }

  List<Book> get historyBooks {
    return _allBooks.where((book) => book.category.contains('تاريخ')).toList();
  }

  List<Book> get miscBooks {
    return _allBooks.where((book) => book.category.contains('منوعات')).toList();
  }

  List<Book> get filteredBooks {
    List<Book> books = _allBooks;

    // Filter by category
    if (_selectedCategory != 'الكل') {
      books = books.where((book) => book.category == _selectedCategory).toList();
    }

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      books = books.where((book) {
        return book.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               book.author.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               book.category.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    return books;
  }

  List<String> get categories {
    return ['الكل', 'الكتب الإسلامية', 'الروايات', 'التاريخ', 'منوعات'];
  }

  // Initialize app with database
  Future<void> _initializeApp() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      // Load books from database
      await _loadBooksFromDatabase();
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error initializing app: $e');
      // Fallback to sample books if database fails
      _allBooks = Book.getSampleBooks();
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadBooksFromDatabase() async {
    try {
      _allBooks = await _databaseService.getAllBooks();
      if (_allBooks.isEmpty) {
        // If no books in database, use sample books
        _allBooks = Book.getSampleBooks();
      }
    } catch (e) {
      print('Error loading books from database: $e');
      // Fallback to sample books
      _allBooks = Book.getSampleBooks();
    }
  }

  // Methods
  void setCurrentPageIndex(int index) {
    _currentPageIndex = index;
    notifyListeners();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    _performSearch();
    notifyListeners();
  }

  void setSelectedCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void _performSearch() {
    if (_searchQuery.isEmpty) {
      _searchResults = [];
    } else {
      _searchResults = _allBooks.where((book) {
        return book.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               book.author.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               book.category.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               book.description?.toLowerCase().contains(_searchQuery.toLowerCase()) == true;
      }).toList();
    }
  }

  Future<void> addBook(Book book) async {
    try {
      await _databaseService.insertBook(book);
      _allBooks.add(book);
      notifyListeners();
    } catch (e) {
      print('Error adding book: $e');
      // Add to local list as fallback
      _allBooks.add(book);
      notifyListeners();
    }
  }

  Future<void> toggleFavorite(String bookId) async {
    final bookIndex = _allBooks.indexWhere((book) => book.id == bookId);
    if (bookIndex != -1) {
      final newFavoriteStatus = !_allBooks[bookIndex].isFavorite;
      
      try {
        await _databaseService.toggleFavorite(bookId, newFavoriteStatus);
        _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
          isFavorite: newFavoriteStatus,
        );
        notifyListeners();
      } catch (e) {
        print('Error toggling favorite: $e');
        // Update local list as fallback
        _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
          isFavorite: newFavoriteStatus,
        );
        notifyListeners();
      }
    }
  }

  Future<void> updateReadingProgress(String bookId, double progress) async {
    final bookIndex = _allBooks.indexWhere((book) => book.id == bookId);
    if (bookIndex != -1) {
      try {
        await _databaseService.updateReadingProgress(bookId, progress);
        _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
          readingProgress: progress,
          lastReadDate: DateTime.now(),
        );
        notifyListeners();
      } catch (e) {
        print('Error updating reading progress: $e');
        // Update local list as fallback
        _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
          readingProgress: progress,
          lastReadDate: DateTime.now(),
        );
        notifyListeners();
      }
    }
  }

  // Get books by category from database
  Future<List<Book>> getBooksByCategory(String categoryName) async {
    try {
      return await _databaseService.getBooksByCategory(categoryName);
    } catch (e) {
      print('Error getting books by category: $e');
      return filteredBooks;
    }
  }

  // Search books in database
  Future<List<Book>> searchBooksInDatabase(String query) async {
    try {
      return await _databaseService.searchBooks(query);
    } catch (e) {
      print('Error searching books: $e');
      return _searchResults;
    }
  }

  // Refresh books from database
  Future<void> refreshBooks() async {
    await _loadBooksFromDatabase();
    notifyListeners();
  }

  void updateUserProfile(User updatedUser) {
    _currentUser = updatedUser;
    notifyListeners();
  }

  void incrementBooksRead() {
    _currentUser = _currentUser.copyWith(
      booksRead: _currentUser.booksRead + 1,
    );
    notifyListeners();
  }

  // Get storage statistics
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final totalBooks = _allBooks.length;
      final localBooks = _allBooks.where((book) => book.hasLocalFile).length;
      final totalSize = _allBooks
          .where((book) => book.fileSizeInMB != null)
          .fold<double>(0, (sum, book) => sum + book.fileSizeInMB!);
      
      return {
        'totalBooks': totalBooks,
        'localBooks': localBooks,
        'totalSizeMB': totalSize,
        'averageSizeMB': localBooks > 0 ? totalSize / localBooks : 0,
      };
    } catch (e) {
      print('Error getting storage stats: $e');
      return {
        'totalBooks': 0,
        'localBooks': 0,
        'totalSizeMB': 0.0,
        'averageSizeMB': 0.0,
      };
    }
  }

  // Get reading statistics
  Map<String, dynamic> getReadingStats() {
    final totalBooks = _allBooks.length;
    final readBooks = _allBooks.where((book) => book.readingProgress >= 1.0).length;
    final inProgressBooks = _allBooks.where((book) => 
        book.readingProgress > 0 && book.readingProgress < 1.0).length;
    final favoriteCount = favoriteBooks.length;
    
    final totalProgress = _allBooks.fold<double>(
        0, (sum, book) => sum + book.readingProgress);
    final averageProgress = totalBooks > 0 ? totalProgress / totalBooks : 0.0;

    return {
      'totalBooks': totalBooks,
      'readBooks': readBooks,
      'inProgressBooks': inProgressBooks,
      'favoriteCount': favoriteCount,
      'averageProgress': averageProgress,
      'readingGoalProgress': _currentUser.booksRead / _currentUser.readingGoal,
    };
  }

  // Get category statistics
  Map<String, int> getCategoryStats() {
    final stats = <String, int>{};
    for (final book in _allBooks) {
      stats[book.category] = (stats[book.category] ?? 0) + 1;
    }
    return stats;
  }

  // Export/Import functionality for backup
  Future<Map<String, dynamic>> exportUserData() async {
    try {
      return {
        'user': {
          'id': _currentUser.id,
          'name': _currentUser.name,
          'email': _currentUser.email,
          'booksRead': _currentUser.booksRead,
          'readingGoal': _currentUser.readingGoal,
          'favoriteGenres': _currentUser.favoriteGenres,
        },
        'books': _allBooks.map((book) => {
          'id': book.id,
          'title': book.title,
          'author': book.author,
          'category': book.category,
          'readingProgress': book.readingProgress,
          'isFavorite': book.isFavorite,
          'lastReadDate': book.lastReadDate?.toIso8601String(),
        }).toList(),
        'exportDate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('Error exporting user data: $e');
      return {};
    }
  }
}
