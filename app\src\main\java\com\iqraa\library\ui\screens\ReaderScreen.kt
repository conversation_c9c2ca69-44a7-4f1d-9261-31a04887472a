package com.iqraa.library.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.viewmodel.BookViewModel
import com.iqraa.library.ui.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReaderScreen(
    bookId: String,
    onBackClick: () -> Unit,
    bookViewModel: BookViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val book by bookViewModel.getBookById(bookId).collectAsState(initial = null)
    val userPreferences by userViewModel.userPreferences.collectAsState(initial = null)
    
    var showSettings by remember { mutableStateOf(false) }
    var showMenu by remember { mutableStateOf(false) }
    var currentPage by remember { mutableStateOf(1) }
    
    val listState = rememberLazyListState()
    
    // Sample book content - in real app, this would be loaded from the book file
    val bookContent = remember {
        generateSampleContent()
    }
    
    book?.let { currentBook ->
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Main reading content
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        Color(android.graphics.Color.parseColor(
                            userPreferences?.backgroundColor ?: "#FFFFFF"
                        ))
                    )
                    .padding(
                        horizontal = 16.dp,
                        vertical = if (showMenu) 80.dp else 40.dp
                    ),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(bookContent.size) { index ->
                    Text(
                        text = bookContent[index],
                        style = MaterialTheme.typography.bodyLarge.copy(
                            fontSize = (userPreferences?.fontSize ?: 16f).sp,
                            lineHeight = ((userPreferences?.fontSize ?: 16f) * (userPreferences?.lineSpacing ?: 1.5f)).sp,
                            color = Color(android.graphics.Color.parseColor(
                                userPreferences?.textColor ?: "#000000"
                            ))
                        ),
                        textAlign = TextAlign.Justify,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            
            // Top Menu Bar
            if (showMenu) {
                TopAppBar(
                    title = { 
                        Text(
                            text = currentBook.title,
                            maxLines = 1
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = onBackClick) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = null
                            )
                        }
                    },
                    actions = {
                        IconButton(onClick = { /* Show table of contents */ }) {
                            Icon(
                                imageVector = Icons.Default.List,
                                contentDescription = stringResource(R.string.table_of_contents)
                            )
                        }
                        
                        IconButton(onClick = { /* Show bookmarks */ }) {
                            Icon(
                                imageVector = Icons.Default.Bookmark,
                                contentDescription = stringResource(R.string.bookmarks)
                            )
                        }
                        
                        IconButton(onClick = { showSettings = true }) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = stringResource(R.string.text_settings)
                            )
                        }
                    },
                    modifier = Modifier.align(Alignment.TopCenter)
                )
            }
            
            // Bottom Navigation Bar
            if (showMenu) {
                BottomAppBar(
                    modifier = Modifier.align(Alignment.BottomCenter)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(
                            onClick = { 
                                if (currentPage > 1) {
                                    currentPage--
                                    // Navigate to previous page
                                }
                            },
                            enabled = currentPage > 1
                        ) {
                            Icon(
                                imageVector = Icons.Default.NavigateBefore,
                                contentDescription = "Previous Page"
                            )
                        }
                        
                        Text(
                            text = "$currentPage / ${currentBook.pageCount}",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        
                        IconButton(
                            onClick = { 
                                if (currentPage < currentBook.pageCount) {
                                    currentPage++
                                    // Navigate to next page
                                }
                            },
                            enabled = currentPage < currentBook.pageCount
                        ) {
                            Icon(
                                imageVector = Icons.Default.NavigateNext,
                                contentDescription = "Next Page"
                            )
                        }
                        
                        IconButton(onClick = { /* Add bookmark */ }) {
                            Icon(
                                imageVector = Icons.Default.BookmarkAdd,
                                contentDescription = stringResource(R.string.add_bookmark)
                            )
                        }
                        
                        IconButton(onClick = { /* Add note */ }) {
                            Icon(
                                imageVector = Icons.Default.Note,
                                contentDescription = stringResource(R.string.add_note)
                            )
                        }
                    }
                }
            }
            
            // Tap to toggle menu
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 32.dp),
                contentAlignment = Alignment.Center
            ) {
                // Invisible clickable area to toggle menu
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Transparent)
                ) {
                    // Left side - previous page
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(0.3f)
                            .align(Alignment.CenterStart)
                    )
                    
                    // Center - toggle menu
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(0.4f)
                            .align(Alignment.Center)
                    )
                    
                    // Right side - next page
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(0.3f)
                            .align(Alignment.CenterEnd)
                    )
                }
            }
        }
        
        // Reading Settings Bottom Sheet
        if (showSettings) {
            ModalBottomSheet(
                onDismissRequest = { showSettings = false }
            ) {
                ReaderSettingsContent(
                    userPreferences = userPreferences,
                    onPreferencesUpdate = { preferences ->
                        userViewModel.updateUserPreferences(preferences)
                    },
                    onDismiss = { showSettings = false }
                )
            }
        }
    } ?: run {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
    
    // Toggle menu visibility on tap
    LaunchedEffect(Unit) {
        showMenu = true
        // Auto-hide menu after 3 seconds
        kotlinx.coroutines.delay(3000)
        showMenu = false
    }
}

@Composable
private fun ReaderSettingsContent(
    userPreferences: com.iqraa.library.data.model.UserPreferences?,
    onPreferencesUpdate: (com.iqraa.library.data.model.UserPreferences) -> Unit,
    onDismiss: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = stringResource(R.string.text_settings),
            style = MaterialTheme.typography.titleLarge,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Font Size
        Text(
            text = stringResource(R.string.font_size),
            style = MaterialTheme.typography.titleMedium
        )
        
        Slider(
            value = userPreferences?.fontSize ?: 16f,
            onValueChange = { newSize ->
                userPreferences?.let { prefs ->
                    onPreferencesUpdate(prefs.copy(fontSize = newSize))
                }
            },
            valueRange = 12f..24f,
            steps = 11
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Line Spacing
        Text(
            text = stringResource(R.string.line_spacing),
            style = MaterialTheme.typography.titleMedium
        )
        
        Slider(
            value = userPreferences?.lineSpacing ?: 1.5f,
            onValueChange = { newSpacing ->
                userPreferences?.let { prefs ->
                    onPreferencesUpdate(prefs.copy(lineSpacing = newSpacing))
                }
            },
            valueRange = 1.0f..2.5f,
            steps = 14
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = onDismiss,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(stringResource(R.string.ok))
        }
    }
}

private fun generateSampleContent(): List<String> {
    return listOf(
        "في قديم الزمان، كان هناك مكتبة عظيمة تحتوي على آلاف الكتب النادرة والمخطوطات القديمة. كانت هذه المكتبة مقصداً للعلماء والباحثين من جميع أنحاء العالم.",
        
        "وفي يوم من الأيام، وصل إلى المكتبة شاب يبحث عن كتاب نادر يحتوي على أسرار الحكمة القديمة. كان هذا الشاب مولعاً بالقراءة منذ صغره، وقد سمع عن هذا الكتاب من أستاذه العجوز.",
        
        "بدأ الشاب رحلته في أروقة المكتبة الواسعة، يتنقل بين الأرفف العالية المليئة بالكتب. كان كل رف يحكي قصة مختلفة، وكل كتاب يحمل في طياته عالماً من المعرفة والخيال.",
        
        "وبينما كان يبحث، التقى بأمين المكتبة العجوز، الذي كان يعمل هناك منذ عقود طويلة. أخبره الأمين أن الكتاب الذي يبحث عنه موجود في القسم السري من المكتبة، والذي لا يُسمح بدخوله إلا للباحثين المتميزين.",
        
        "تحدى الشاب نفسه وقرر أن يثبت جدارته للحصول على إذن دخول القسم السري. بدأ بقراءة العديد من الكتب في مختلف المجالات، وأظهر فهماً عميقاً وشغفاً حقيقياً بالمعرفة.",
        
        "بعد أشهر من الدراسة والبحث، منحه أمين المكتبة الإذن المطلوب. دخل الشاب إلى القسم السري، حيث وجد الكتاب الذي كان يبحث عنه. كان الكتاب مكتوباً بخط جميل ومزيناً برسوم رائعة.",
        
        "عندما فتح الكتاب، اكتشف أن الحكمة الحقيقية لا تكمن في الكلمات المكتوبة فحسب، بل في الرحلة التي خاضها للوصول إليها. فهم أن المعرفة ليست مجرد معلومات نحفظها، بل تجربة نعيشها ونتعلم منها.",
        
        "ومن ذلك اليوم، أصبح الشاب زائراً دائماً للمكتبة، ليس فقط للبحث عن الكتب، بل لمشاركة ما تعلمه مع الآخرين. وهكذا، تحولت المكتبة من مجرد مكان للكتب إلى مجتمع للمعرفة والتعلم المستمر."
    )
}
