import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../widgets/book_card.dart';
import '../utils/app_theme.dart';

class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مكتبتي'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: AppTheme.arabicGold,
          tabs: const [
            Tab(text: 'المحملة'),
            Tab(text: 'المفضلة'),
            Tab(text: 'قراءة حالية'),
            Tab(text: 'مكتملة'),
          ],
        ),
      ),
      body: Consumer<AppProvider>(
        builder: (context, provider, child) {
          return TabBarView(
            controller: _tabController,
            children: [
              _buildBooksTab(provider.downloadedBooks, 'لا توجد كتب محملة', Icons.download),
              _buildBooksTab(provider.favoriteBooks, 'لا توجد كتب مفضلة', Icons.favorite),
              _buildBooksTab(provider.currentlyReading, 'لا توجد كتب قيد القراءة', Icons.bookmark),
              _buildBooksTab(provider.completedBooks, 'لا توجد كتب مكتملة', Icons.check_circle),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBooksTab(List books, String emptyMessage, IconData emptyIcon) {
    if (books.isEmpty) {
      return _buildEmptyState(emptyMessage, emptyIcon);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: books.length,
      itemBuilder: (context, index) {
        return BookCard(
          book: books[index],
          isHorizontal: true,
          showProgress: true,
          onTap: () => _openBookDetails(books[index]),
        );
      },
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 64,
              color: Theme.of(context).primaryColor.withOpacity(0.5),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            message,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة كتب إلى مكتبتك',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              context.read<AppProvider>().setCurrentPageIndex(2);
            },
            icon: const Icon(Icons.search),
            label: const Text('استكشف الكتب'),
          ),
        ],
      ),
    );
  }

  void _openBookDetails(book) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح تفاصيل كتاب: ${book.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
