package com.iqraa.library.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "categories")
data class Category(
    @PrimaryKey
    val id: String,
    val name: String,
    val nameEn: String,
    val description: String,
    val iconUrl: String? = null,
    val color: String = "#2196F3",
    val bookCount: Int = 0,
    val isPopular: Boolean = false,
    val sortOrder: Int = 0
)

// Predefined categories for Arabic library
object DefaultCategories {
    val categories = listOf(
        Category("1", "الأدب العربي", "Arabic Literature", "كتب الأدب والشعر العربي"),
        Category("2", "الروايات", "Novels", "الروايات العربية والمترجمة"),
        Category("3", "العلوم الإسلامية", "Islamic Sciences", "كتب الفقه والتفسير والحديث"),
        Category("4", "التاريخ", "History", "كتب التاريخ العربي والإسلامي"),
        Category("5", "الفلسفة", "Philosophy", "كتب الفلسفة والفكر"),
        Category("6", "العلوم", "Sciences", "كتب العلوم الطبيعية والرياضيات"),
        Category("7", "التنمية الذاتية", "Self Development", "كتب تطوير الذات والمهارات"),
        Category("8", "الطب", "Medicine", "كتب الطب والصحة"),
        Category("9", "الاقتصاد", "Economics", "كتب الاقتصاد والمال"),
        Category("10", "التكنولوجيا", "Technology", "كتب التقنية والبرمجة"),
        Category("11", "الأطفال", "Children", "كتب الأطفال والناشئة"),
        Category("12", "الشعر", "Poetry", "دواوين الشعر العربي")
    )
}
