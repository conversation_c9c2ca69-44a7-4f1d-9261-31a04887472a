package com.iqraa.library.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String,
    val username: String,
    val email: String,
    val displayName: String,
    val profileImageUrl: String? = null,
    val joinDate: Long = System.currentTimeMillis(),
    val booksRead: Int = 0,
    val totalReadingTime: Long = 0, // in minutes
    val favoriteGenres: List<String> = emptyList(),
    val readingGoal: Int = 0, // books per year
    val isVerified: Boolean = false,
    val bio: String? = null
)

@Entity(tableName = "user_preferences")
data class UserPreferences(
    @PrimaryKey
    val userId: String,
    val isDarkMode: Boolean = false,
    val fontSize: Float = 16f,
    val fontFamily: String = "Amiri",
    val lineSpacing: Float = 1.5f,
    val backgroundColor: String = "#FFFFFF",
    val textColor: String = "#000000",
    val isRTL: Boolean = true,
    val autoNightMode: Boolean = false,
    val downloadOnWifiOnly: Boolean = true,
    val notificationsEnabled: Boolean = true
)
