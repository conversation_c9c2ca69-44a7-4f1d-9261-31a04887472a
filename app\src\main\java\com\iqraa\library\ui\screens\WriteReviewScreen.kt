package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.viewmodel.ReviewViewModel
import com.iqraa.library.ui.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WriteReviewScreen(
    bookId: String,
    bookTitle: String,
    onBackClick: () -> Unit,
    onReviewSubmitted: () -> Unit,
    reviewViewModel: ReviewViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val currentUser by userViewModel.currentUser.collectAsState(initial = null)
    val isLoading by reviewViewModel.isLoading.collectAsState()
    val error by reviewViewModel.error.collectAsState()
    
    var rating by remember { mutableStateOf(0f) }
    var reviewTitle by remember { mutableStateOf("") }
    var reviewContent by remember { mutableStateOf("") }
    var containsSpoilers by remember { mutableStateOf(false) }
    
    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { Text(stringResource(R.string.write_review)) },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            },
            actions = {
                TextButton(
                    onClick = {
                        currentUser?.let { user ->
                            reviewViewModel.submitReview(
                                bookId = bookId,
                                userId = user.id,
                                userName = user.displayName,
                                userAvatar = user.profileImageUrl,
                                rating = rating,
                                title = reviewTitle,
                                content = reviewContent,
                                containsSpoilers = containsSpoilers
                            )
                        }
                    },
                    enabled = rating > 0 && reviewContent.isNotBlank() && !isLoading
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(stringResource(R.string.submit_review))
                    }
                }
            }
        )
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Book Info
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "مراجعة كتاب",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = bookTitle,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
            
            // Rating Section
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "التقييم",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        repeat(5) { index ->
                            IconButton(
                                onClick = { rating = (index + 1).toFloat() }
                            ) {
                                Icon(
                                    imageVector = if (index < rating.toInt()) {
                                        Icons.Default.Star
                                    } else {
                                        Icons.Default.StarBorder
                                    },
                                    contentDescription = null,
                                    tint = if (index < rating.toInt()) {
                                        MaterialTheme.colorScheme.primary
                                    } else {
                                        MaterialTheme.colorScheme.onSurfaceVariant
                                    },
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        }
                    }
                    
                    if (rating > 0) {
                        Text(
                            text = getRatingText(rating.toInt()),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
            
            // Review Title
            OutlinedTextField(
                value = reviewTitle,
                onValueChange = { reviewTitle = it },
                label = { Text(stringResource(R.string.review_title)) },
                placeholder = { Text("عنوان مختصر لمراجعتك") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
            
            // Review Content
            OutlinedTextField(
                value = reviewContent,
                onValueChange = { reviewContent = it },
                label = { Text(stringResource(R.string.review_content)) },
                placeholder = { Text("شاركنا رأيك في الكتاب...") },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                maxLines = 10
            )
            
            // Spoilers Checkbox
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = containsSpoilers,
                    onCheckedChange = { containsSpoilers = it }
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "تحتوي المراجعة على حرق للأحداث",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            // Guidelines
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "إرشادات كتابة المراجعة",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    val guidelines = listOf(
                        "اكتب مراجعة صادقة ومفيدة للقراء الآخرين",
                        "تجنب الحرق المباشر للأحداث دون تحذير",
                        "ركز على جودة الكتابة والمحتوى والأسلوب",
                        "احترم آراء الآخرين وتجنب التعليقات المسيئة"
                    )
                    
                    guidelines.forEach { guideline ->
                        Row(
                            modifier = Modifier.padding(vertical = 2.dp)
                        ) {
                            Text(
                                text = "• ",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                            
                            Text(
                                text = guideline,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
            
            // Error Message
            if (error != null) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Error,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onErrorContainer
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = error,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
    
    // Handle successful submission
    LaunchedEffect(isLoading) {
        if (!isLoading && error == null && rating > 0 && reviewContent.isNotBlank()) {
            // Check if review was submitted successfully
            onReviewSubmitted()
        }
    }
}

private fun getRatingText(rating: Int): String {
    return when (rating) {
        1 -> "ضعيف جداً"
        2 -> "ضعيف"
        3 -> "متوسط"
        4 -> "جيد"
        5 -> "ممتاز"
        else -> ""
    }
}
