package com.iqraa.library.data.database

import androidx.room.*
import com.iqraa.library.data.model.Note
import com.iqraa.library.data.model.Quote
import com.iqraa.library.data.model.NoteType
import kotlinx.coroutines.flow.Flow

@Dao
interface NoteDao {
    
    @Query("SELECT * FROM notes WHERE bookId = :bookId AND userId = :userId ORDER BY pageNumber ASC")
    fun getNotesForBook(bookId: String, userId: String): Flow<List<Note>>
    
    @Query("SELECT * FROM notes WHERE bookId = :bookId AND userId = :userId AND noteType = :noteType ORDER BY pageNumber ASC")
    fun getNotesByType(bookId: String, userId: String, noteType: NoteType): Flow<List<Note>>
    
    @Query("SELECT * FROM notes WHERE userId = :userId ORDER BY updatedAt DESC")
    fun getAllUserNotes(userId: String): Flow<List<Note>>
    
    @Query("SELECT * FROM notes WHERE id = :noteId")
    suspend fun getNoteById(noteId: String): Note?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNote(note: Note)
    
    @Update
    suspend fun updateNote(note: Note)
    
    @Delete
    suspend fun deleteNote(note: Note)
    
    @Query("DELETE FROM notes WHERE bookId = :bookId AND userId = :userId")
    suspend fun deleteAllNotesForBook(bookId: String, userId: String)
    
    // Quotes
    @Query("SELECT * FROM quotes WHERE userId = :userId ORDER BY createdAt DESC")
    fun getUserQuotes(userId: String): Flow<List<Quote>>
    
    @Query("SELECT * FROM quotes WHERE bookId = :bookId ORDER BY createdAt DESC")
    fun getQuotesForBook(bookId: String): Flow<List<Quote>>
    
    @Query("SELECT * FROM quotes WHERE isShared = 1 ORDER BY likesCount DESC, createdAt DESC")
    fun getPublicQuotes(): Flow<List<Quote>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertQuote(quote: Quote)
    
    @Update
    suspend fun updateQuote(quote: Quote)
    
    @Delete
    suspend fun deleteQuote(quote: Quote)
    
    @Query("UPDATE quotes SET shareCount = shareCount + 1 WHERE id = :quoteId")
    suspend fun incrementShareCount(quoteId: String)
    
    @Query("UPDATE quotes SET likesCount = likesCount + 1 WHERE id = :quoteId")
    suspend fun incrementLikesCount(quoteId: String)
}
