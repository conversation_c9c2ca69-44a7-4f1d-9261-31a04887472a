package com.iqraa.library.utils

import android.content.Context
import android.os.Environment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DownloadManager @Inject constructor(
    private val context: Context,
    private val okHttpClient: OkHttpClient
) {
    
    data class DownloadProgress(
        val bytesDownloaded: Long,
        val totalBytes: Long,
        val progress: Float,
        val isCompleted: Boolean,
        val error: String? = null
    )
    
    fun downloadBook(
        url: String,
        fileName: String,
        bookId: String
    ): Flow<DownloadProgress> = flow {
        try {
            val request = Request.Builder()
                .url(url)
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                emit(DownloadProgress(0, 0, 0f, false, "فشل في تحميل الملف"))
                return@flow
            }
            
            val body = response.body ?: run {
                emit(DownloadProgress(0, 0, 0f, false, "الملف فارغ"))
                return@flow
            }
            
            val totalBytes = body.contentLength()
            val inputStream = body.byteStream()
            
            // Create downloads directory
            val downloadsDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "books")
            if (!downloadsDir.exists()) {
                downloadsDir.mkdirs()
            }
            
            val file = File(downloadsDir, fileName)
            val outputStream = FileOutputStream(file)
            
            val buffer = ByteArray(8192)
            var bytesDownloaded = 0L
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                bytesDownloaded += bytesRead
                
                val progress = if (totalBytes > 0) {
                    (bytesDownloaded.toFloat() / totalBytes.toFloat())
                } else {
                    0f
                }
                
                emit(DownloadProgress(bytesDownloaded, totalBytes, progress, false))
            }
            
            outputStream.close()
            inputStream.close()
            
            emit(DownloadProgress(bytesDownloaded, totalBytes, 1f, true))
            
        } catch (e: IOException) {
            emit(DownloadProgress(0, 0, 0f, false, "خطأ في التحميل: ${e.message}"))
        } catch (e: Exception) {
            emit(DownloadProgress(0, 0, 0f, false, "خطأ غير متوقع: ${e.message}"))
        }
    }.flowOn(Dispatchers.IO)
    
    fun getDownloadedBookPath(fileName: String): String? {
        val downloadsDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "books")
        val file = File(downloadsDir, fileName)
        return if (file.exists()) file.absolutePath else null
    }
    
    fun deleteDownloadedBook(fileName: String): Boolean {
        val downloadsDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "books")
        val file = File(downloadsDir, fileName)
        return if (file.exists()) file.delete() else false
    }
    
    fun getDownloadedBooksSize(): Long {
        val downloadsDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), "books")
        return if (downloadsDir.exists()) {
            downloadsDir.walkTopDown().filter { it.isFile }.map { it.length() }.sum()
        } else {
            0L
        }
    }
    
    fun formatFileSize(bytes: Long): String {
        val kb = bytes / 1024.0
        val mb = kb / 1024.0
        val gb = mb / 1024.0
        
        return when {
            gb >= 1 -> String.format("%.1f GB", gb)
            mb >= 1 -> String.format("%.1f MB", mb)
            kb >= 1 -> String.format("%.1f KB", kb)
            else -> "$bytes B"
        }
    }
}
