import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:path_provider/path_provider.dart';
import '../models/book.dart';
import '../providers/offline_app_provider.dart';

class OfflinePdfReader extends StatefulWidget {
  final Book book;
  final String pdfPath;

  const OfflinePdfReader({
    super.key,
    required this.book,
    required this.pdfPath,
  });

  @override
  State<OfflinePdfReader> createState() => _OfflinePdfReaderState();
}

class _OfflinePdfReaderState extends State<OfflinePdfReader> {
  int _currentPage = 1;
  int _totalPages = 0;
  bool _isLoading = true;
  bool _showControls = true;
  bool _isNightMode = false;

  // Reading settings
  double _fontSize = 16.0;
  Color _backgroundColor = Colors.white;
  Color _textColor = Colors.black;

  @override
  void initState() {
    super.initState();
    _loadPdfFile();
  }

  Future<void> _loadPdfFile() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Check if it's an asset file
      if (widget.pdfPath.startsWith('assets/')) {
        // Copy asset to temporary directory for reading
        final byteData = await rootBundle.load(widget.pdfPath);
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/${widget.book.id}.pdf');
        await tempFile.writeAsBytes(byteData.buffer.asUint8List());
        // File is ready for reading
      } else {
        // Use existing file
        final pdfFile = File(widget.pdfPath);
        // Verify file exists
        if (!await pdfFile.exists()) {
          throw Exception('PDF file not found: ${widget.pdfPath}');
        }
      }

      // Simulate getting page count (in real implementation, use PDF library)
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        setState(() {
          _totalPages = widget.book.pageCount ?? 100;
          _isLoading = false;
          _currentPage = _getLastReadPage();
        });
        _updateReadingProgress();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorDialog('خطأ في تحميل الملف: $e');
      }
    }
  }

  int _getLastReadPage() {
    // Calculate page from reading progress
    if (widget.book.readingProgress > 0) {
      return ((widget.book.readingProgress * _totalPages).round())
          .clamp(1, _totalPages);
    }
    return 1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _isNightMode ? Colors.black : Colors.white,
      body: Stack(
        children: [
          // PDF Content Area
          GestureDetector(
            onTap: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: _backgroundColor,
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : _buildPdfContent(),
            ),
          ),

          // Top Controls
          if (_showControls)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildTopControls(),
            ),

          // Bottom Controls
          if (_showControls)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: _buildBottomControls(),
            ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل الكتاب...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPdfContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Book header
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(top: 80, bottom: 20),
            decoration: BoxDecoration(
              color: _isNightMode ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.picture_as_pdf,
                  size: 64,
                  color: _isNightMode ? Colors.white70 : Colors.grey[600],
                ),
                const SizedBox(height: 16),
                Text(
                  widget.book.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _textColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.book.author,
                  style: TextStyle(
                    fontSize: 16,
                    color: _textColor.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'الصفحة $_currentPage من $_totalPages',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // Sample content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _backgroundColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _isNightMode ? Colors.grey[700]! : Colors.grey[300]!,
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'محتوى الصفحة $_currentPage',
                      style: TextStyle(
                        fontSize: _fontSize + 4,
                        fontWeight: FontWeight.bold,
                        color: _textColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _getSampleContent(),
                      style: TextStyle(
                        fontSize: _fontSize,
                        height: 1.8,
                        color: _textColor,
                      ),
                      textAlign: TextAlign.justify,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 100), // Space for bottom controls
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.8),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      widget.book.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      widget.book.author,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  _isNightMode ? Icons.light_mode : Icons.dark_mode,
                  color: Colors.white,
                ),
                onPressed: _toggleNightMode,
              ),
              IconButton(
                icon: const Icon(Icons.bookmark_border, color: Colors.white),
                onPressed: _addBookmark,
              ),
              IconButton(
                icon: const Icon(Icons.settings, color: Colors.white),
                onPressed: _showSettingsMenu,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Colors.black.withOpacity(0.8),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Progress bar
              Row(
                children: [
                  Text(
                    '$_currentPage',
                    style: const TextStyle(color: Colors.white),
                  ),
                  Expanded(
                    child: Slider(
                      value: _currentPage.toDouble(),
                      min: 1,
                      max: _totalPages.toDouble(),
                      onChanged: (value) {
                        setState(() {
                          _currentPage = value.toInt();
                        });
                        _updateReadingProgress();
                      },
                      activeColor: Theme.of(context).primaryColor,
                      inactiveColor: Colors.white30,
                    ),
                  ),
                  Text(
                    '$_totalPages',
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),

              // Control buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  IconButton(
                    icon: const Icon(Icons.text_decrease, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _fontSize = (_fontSize - 2).clamp(12.0, 24.0);
                      });
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.skip_previous, color: Colors.white),
                    onPressed: _currentPage > 1 ? _previousPage : null,
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '$_currentPage / $_totalPages',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.skip_next, color: Colors.white),
                    onPressed: _currentPage < _totalPages ? _nextPage : null,
                  ),
                  IconButton(
                    icon: const Icon(Icons.text_increase, color: Colors.white),
                    onPressed: () {
                      setState(() {
                        _fontSize = (_fontSize + 2).clamp(12.0, 24.0);
                      });
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      _updateReadingProgress();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
      _updateReadingProgress();
    }
  }

  void _updateReadingProgress() {
    if (_totalPages > 0) {
      final progress = _currentPage / _totalPages;
      context
          .read<OfflineAppProvider>()
          .updateReadingProgress(widget.book.id, progress);
    }
  }

  void _toggleNightMode() {
    setState(() {
      _isNightMode = !_isNightMode;
      if (_isNightMode) {
        _backgroundColor = Colors.grey[900]!;
        _textColor = Colors.white;
      } else {
        _backgroundColor = Colors.white;
        _textColor = Colors.black;
      }
    });
  }

  void _addBookmark() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة إشارة مرجعية للصفحة $_currentPage'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Icon(_isNightMode ? Icons.light_mode : Icons.dark_mode),
              title: const Text('الوضع الليلي'),
              trailing: Switch(
                value: _isNightMode,
                onChanged: (value) {
                  Navigator.pop(context);
                  _toggleNightMode();
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.text_fields),
              title: const Text('حجم النص'),
              subtitle: Text('${_fontSize.toInt()}'),
              onTap: () {
                Navigator.pop(context);
                _showFontSizeDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('الإشارات المرجعية'),
              onTap: () {
                Navigator.pop(context);
                _showBookmarks();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الكتاب'),
              onTap: () {
                Navigator.pop(context);
                _showBookInfo();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showFontSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم النص'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الحجم الحالي: ${_fontSize.toInt()}'),
            Slider(
              value: _fontSize,
              min: 12.0,
              max: 24.0,
              divisions: 6,
              onChanged: (value) {
                setState(() {
                  _fontSize = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showBookmarks() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشارات المرجعية'),
        content: const Text('لا توجد إشارات مرجعية محفوظة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showBookInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.book.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المؤلف: ${widget.book.author}'),
            Text('التصنيف: ${widget.book.category}'),
            Text('عدد الصفحات: $_totalPages'),
            Text('الصفحة الحالية: $_currentPage'),
            Text(
                'التقدم: ${((_currentPage / _totalPages) * 100).toStringAsFixed(1)}%'),
            if (widget.book.fileSizeInMB != null)
              Text(
                  'حجم الملف: ${widget.book.fileSizeInMB!.toStringAsFixed(1)} MB'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  String _getSampleContent() {
    // Sample content based on book category
    if (widget.book.category.contains('إسلامية')) {
      return '''
بسم الله الرحمن الرحيم

هذا محتوى تجريبي لكتاب إسلامي. في التطبيق الحقيقي، سيتم عرض محتوى الكتاب الفعلي من ملف PDF.

الحمد لله رب العالمين، والصلاة والسلام على أشرف المرسلين، سيدنا محمد وعلى آله وصحبه أجمعين.

أما بعد، فإن العلم الشرعي هو أشرف العلوم وأنفعها للعبد في دنياه وآخرته، وقد حث الإسلام على طلب العلم وجعله فريضة على كل مسلم ومسلمة.

وفي هذا الكتاب نستعرض أهم المسائل والأحكام التي يحتاجها المسلم في حياته اليومية، مع الاستدلال بالكتاب والسنة وأقوال العلماء المعتبرين.

نسأل الله تعالى أن ينفع بهذا العمل، وأن يجعله خالصاً لوجهه الكريم.
      ''';
    } else if (widget.book.category.contains('رواية')) {
      return '''
الفصل الأول

كانت الشمس تغرب خلف التلال البعيدة، ملقية بظلالها الذهبية على القرية الصغيرة. في هذا المكان الهادئ، حيث تتداخل الذكريات مع الواقع، تبدأ حكايتنا.

محمد، الشاب الثلاثيني، يقف أمام نافذة منزله القديم، يتأمل المشهد الذي طالما أحبه منذ طفولته. كل شيء هنا يحمل ذكرى، كل حجر يحكي قصة.

في هذا المساء، وصلته رسالة غيرت مجرى حياته تماماً. رسالة من الماضي، تحمل أسراراً ظن أنها دُفنت إلى الأبد.

وهكذا تبدأ رحلة البحث عن الحقيقة، رحلة ستأخذه عبر دروب لم يسلكها من قبل، وستكشف له جوانب من نفسه لم يكن يعرفها.
      ''';
    } else if (widget.book.category.contains('تاريخ')) {
      return '''
الباب الأول: مقدمة في علم التاريخ

إن علم التاريخ من أجل العلوم وأنفعها، إذ به نتعرف على أحوال الأمم السابقة وما جرى لها من أحداث وتطورات. وقد اهتم المسلمون بالتاريخ اهتماماً بالغاً، فألفوا فيه المؤلفات الجليلة التي لا تزال مراجع معتمدة إلى يومنا هذا.

والتاريخ ليس مجرد سرد للأحداث، بل هو دراسة للأسباب والنتائج، وتحليل للعوامل التي أثرت في مجرى الأحداث. من خلاله نستطيع أن نفهم الحاضر ونستشرف المستقبل.

في هذا الكتاب، نستعرض أهم الأحداث التاريخية التي شكلت مسار الحضارة الإسلامية، مع التركيز على الدروس والعبر المستفادة منها.

وقد اعتمدنا في هذا العمل على أوثق المصادر التاريخية، مع مراعاة المنهج العلمي في النقد والتحليل.
      ''';
    } else {
      return '''
مقدمة

يسعدني أن أقدم لكم هذا الكتاب، الذي يتناول موضوعاً مهماً في حياتنا المعاصرة. لقد حرصت على أن يكون المحتوى مفيداً وعملياً، يمكن للقارئ الاستفادة منه في حياته اليومية.

إن الهدف من هذا العمل هو تقديم المعرفة بأسلوب سهل ومبسط، مع الحرص على الدقة العلمية والوضوح في العرض.

أتمنى أن يجد القارئ في هذا الكتاب ما يبحث عنه من معلومات مفيدة وأفكار جديدة تساعده في تطوير نفسه وتحسين حياته.

وأخيراً، أشكر كل من ساهم في إنجاز هذا العمل، وأسأل الله تعالى أن ينفع به.

المؤلف
      ''';
    }
  }
}
