import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/book.dart';
import '../providers/offline_app_provider.dart';
import '../widgets/book_card.dart';
import '../utils/app_theme.dart';
import 'add_book_screen.dart';
import 'book_details_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: AppTheme.gradientDecoration,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Consumer<OfflineAppProvider>(
                          builder: (context, provider, child) {
                            return Text(
                              'مرحباً، ${provider.currentUser.name}',
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                            );
                          },
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'ماذا تريد أن تقرأ اليوم؟',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.white70,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.add, color: Colors.white),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddBookScreen(),
                    ),
                  );
                },
              ),
              IconButton(
                icon: const Icon(Icons.notifications_outlined,
                    color: Colors.white),
                onPressed: () {},
              ),
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () {
                  context.read<OfflineAppProvider>().setCurrentPageIndex(2);
                },
              ),
            ],
          ),

          // Content
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Reading Progress Card
                _buildReadingProgressCard(),

                const SizedBox(height: 24),

                // Continue Reading Section
                _buildContinueReadingSection(),

                const SizedBox(height: 24),

                // Categories Section
                _buildCategoriesSection(),

                const SizedBox(height: 24),

                // Recent Books Section
                _buildRecentBooksSection(),

                const SizedBox(height: 24),

                // Recommended Books Section
                _buildRecommendedBooksSection(),

                const SizedBox(height: 24),

                // Reading Stats
                _buildReadingStatsCard(),

                const SizedBox(
                    height: 100), // Bottom padding for navigation bar
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReadingProgressCard() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final user = provider.currentUser;
        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: AppTheme.goldCardDecoration,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.emoji_events,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'هدف القراءة لهذا العام',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${user.booksRead} من ${user.readingGoal} كتاب',
                          style: Theme.of(context)
                              .textTheme
                              .headlineSmall
                              ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: user.goalProgress,
                          backgroundColor: Colors.white.withOpacity(0.3),
                          valueColor:
                              const AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${(user.goalProgress * 100).toInt()}% مكتمل',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.white70,
                                  ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '${user.currentStreak}',
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        Text(
                          'أيام متتالية',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.white70,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContinueReadingSection() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final currentlyReading = provider.continueReadingBooks;

        if (currentlyReading.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'تابع القراءة',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  TextButton(
                    onPressed: () {
                      provider.setCurrentPageIndex(1);
                    },
                    child: const Text('عرض الكل'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 4),
                itemCount: currentlyReading.length,
                itemBuilder: (context, index) {
                  return BookCard(
                    book: currentlyReading[index],
                    showProgress: true,
                    onTap: () => _openBookDetails(currentlyReading[index]),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoriesSection() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final categories =
            provider.categories.where((cat) => cat != 'الكل').take(6).toList();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'التصنيفات الشائعة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 16),
                itemCount: categories.length,
                itemBuilder: (context, index) {
                  return _buildCategoryCard(categories[index]);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryCard(String category) {
    final colors = [
      AppTheme.arabicBlue,
      AppTheme.arabicGreen,
      AppTheme.arabicGold,
      Colors.purple,
      Colors.orange,
      Colors.teal,
    ];

    final color = colors[category.hashCode % colors.length];

    return GestureDetector(
      onTap: () {
        context.read<OfflineAppProvider>().setSelectedCategory(category);
        context.read<OfflineAppProvider>().setCurrentPageIndex(2);
      },
      child: Container(
        width: 120,
        margin: const EdgeInsets.only(left: 12),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  color.withOpacity(0.1),
                  color.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  category,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentBooksSection() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final recentBooks = provider.recentBooks;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المضاف حديثاً',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  TextButton(
                    onPressed: () {
                      provider.setCurrentPageIndex(2);
                    },
                    child: const Text('عرض الكل'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 4),
                itemCount: recentBooks.length,
                itemBuilder: (context, index) {
                  return BookCard(
                    book: recentBooks[index],
                    onTap: () => _openBookDetails(recentBooks[index]),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecommendedBooksSection() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final recommendedBooks = provider.allBooks.take(5).toList();

        if (recommendedBooks.isEmpty) {
          return const SizedBox.shrink();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const Icon(
                    Icons.recommend,
                    color: AppTheme.arabicGold,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'مقترح لك',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 280,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 4),
                itemCount: recommendedBooks.length,
                itemBuilder: (context, index) {
                  return BookCard(
                    book: recommendedBooks[index],
                    onTap: () => _openBookDetails(recommendedBooks[index]),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildReadingStatsCard() {
    return Consumer<OfflineAppProvider>(
      builder: (context, provider, child) {
        final user = provider.currentUser;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إحصائيات القراءة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatItem(
                        context,
                        '${user.booksRead}',
                        'كتب مقروءة',
                        Icons.book,
                        AppTheme.arabicBlue,
                      ),
                      _buildStatItem(
                        context,
                        '${provider.continueReadingBooks.length}',
                        'قراءة حالية',
                        Icons.bookmark,
                        AppTheme.arabicGreen,
                      ),
                      _buildStatItem(
                        context,
                        user.readingTimeFormatted,
                        'وقت القراءة',
                        Icons.access_time,
                        AppTheme.arabicGold,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String value,
    String label,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  void _openBookDetails(Book book) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BookDetailsScreen(book: book),
      ),
    );
  }
}
