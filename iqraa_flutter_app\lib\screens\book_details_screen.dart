import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/book.dart';
import '../providers/app_provider.dart';
import '../utils/app_theme.dart';
import '../services/file_service.dart';
import 'pdf_reader_screen.dart';

class BookDetailsScreen extends StatelessWidget {
  final Book book;

  const BookDetailsScreen({
    super.key,
    required this.book,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar with Book Cover
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  CachedNetworkImage(
                    imageUrl: book.coverUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.book, size: 100),
                    ),
                  ),
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Book Details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Author
                  Text(
                    book.title,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    book.author,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),

                  const SizedBox(height: 16),

                  // Rating and Reviews
                  Row(
                    children: [
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < book.rating.floor()
                                ? Icons.star
                                : index < book.rating
                                    ? Icons.star_half
                                    : Icons.star_border,
                            color: Colors.amber,
                            size: 20,
                          );
                        }),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${book.rating.toStringAsFixed(1)} (${book.reviewCount} مراجعة)',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Book Info Cards
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoCard(
                          context,
                          'التصنيف',
                          book.category,
                          Icons.category,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoCard(
                          context,
                          'الصفحات',
                          '${book.pageCount ?? 'غير محدد'}',
                          Icons.menu_book,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoCard(
                          context,
                          'اللغة',
                          book.language ?? 'العربية',
                          Icons.language,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildInfoCard(
                          context,
                          'سنة النشر',
                          book.publishDate ?? 'غير محدد',
                          Icons.calendar_today,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Tags
                  if (book.tags.isNotEmpty) ...[
                    Text(
                      'الكلمات المفتاحية',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: book.tags.map((tag) {
                        return Chip(
                          label: Text(tag),
                          backgroundColor:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          labelStyle: TextStyle(
                            color: Theme.of(context).primaryColor,
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Description
                  if (book.description != null) ...[
                    Text(
                      'وصف الكتاب',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      book.description!,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.justify,
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Reading Progress (if started)
                  if (book.readingProgress > 0) ...[
                    Text(
                      'تقدم القراءة',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: book.readingProgress,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(book.readingProgress * 100).toInt()}% مكتمل',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Action Buttons
                  Consumer<AppProvider>(
                    builder: (context, provider, child) {
                      return Column(
                        children: [
                          // Primary Action Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: () =>
                                  _handlePrimaryAction(context, provider),
                              icon: Icon(_getPrimaryActionIcon()),
                              label: Text(_getPrimaryActionText()),
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                            ),
                          ),

                          const SizedBox(height: 12),

                          // Secondary Actions
                          Row(
                            children: [
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () =>
                                      provider.toggleFavorite(book.id),
                                  icon: Icon(
                                    book.isFavorite
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    color: book.isFavorite ? Colors.red : null,
                                  ),
                                  label: Text(book.isFavorite
                                      ? 'مفضل'
                                      : 'إضافة للمفضلة'),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: () => _shareBook(context),
                                  icon: const Icon(Icons.share),
                                  label: const Text('مشاركة'),
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),

                  const SizedBox(height: 100), // Bottom padding
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
      BuildContext context, String title, String value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getPrimaryActionIcon() {
    if (book.readingProgress >= 1.0) {
      return Icons.check_circle;
    } else if (book.readingProgress > 0) {
      return Icons.play_arrow;
    } else {
      return Icons.menu_book;
    }
  }

  String _getPrimaryActionText() {
    if (book.readingProgress >= 1.0) {
      return 'مكتمل';
    } else if (book.readingProgress > 0) {
      return 'متابعة القراءة';
    } else {
      return 'بدء القراءة';
    }
  }

  void _handlePrimaryAction(BuildContext context, AppProvider provider) {
    if (book.readingProgress >= 1.0) {
      // Book completed - maybe show review dialog
      _showReviewDialog(context);
    } else if (book.readingProgress > 0) {
      // Continue reading
      _openReader(context);
    } else {
      // Start reading
      provider.updateReadingProgress(book.id, 0.1);
      _openReader(context);
    }
  }

  void _openReader(BuildContext context) {
    if (book.hasLocalFile && book.localFilePath != null) {
      // Open PDF reader
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PdfReaderScreen(
            book: book,
            pdfPath: book.localFilePath!,
          ),
        ),
      );
    } else {
      // Show message that PDF is not available
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('لا يوجد ملف PDF لهذا الكتاب'),
          action: SnackBarAction(
            label: 'موافق',
            onPressed: () {},
          ),
        ),
      );
    }
  }

  void _shareBook(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('مشاركة كتاب: ${book.title}'),
      ),
    );
  }

  void _showReviewDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تقييم الكتاب'),
        content: const Text('هل أعجبك هذا الكتاب؟ شاركنا رأيك!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لاحقاً'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Open review screen
            },
            child: const Text('تقييم'),
          ),
        ],
      ),
    );
  }
}
