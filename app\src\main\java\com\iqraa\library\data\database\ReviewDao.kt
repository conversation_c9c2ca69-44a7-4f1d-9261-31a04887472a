package com.iqraa.library.data.database

import androidx.room.*
import com.iqraa.library.data.model.Review
import com.iqraa.library.data.model.UserBookRating
import kotlinx.coroutines.flow.Flow

@Dao
interface ReviewDao {
    
    @Query("SELECT * FROM reviews WHERE bookId = :bookId ORDER BY createdAt DESC")
    fun getReviewsForBook(bookId: String): Flow<List<Review>>
    
    @Query("SELECT * FROM reviews WHERE userId = :userId ORDER BY createdAt DESC")
    fun getReviewsByUser(userId: String): Flow<List<Review>>
    
    @Query("SELECT * FROM reviews WHERE id = :reviewId")
    suspend fun getReviewById(reviewId: String): Review?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReview(review: Review)
    
    @Update
    suspend fun updateReview(review: Review)
    
    @Delete
    suspend fun deleteReview(review: Review)
    
    @Query("SELECT AVG(rating) FROM reviews WHERE bookId = :bookId")
    suspend fun getAverageRatingForBook(bookId: String): Float?
    
    @Query("SELECT COUNT(*) FROM reviews WHERE bookId = :bookId")
    suspend fun getReviewCountForBook(bookId: String): Int
    
    // User Book Ratings
    @Query("SELECT * FROM user_book_ratings WHERE userId = :userId AND bookId = :bookId")
    suspend fun getUserRatingForBook(userId: String, bookId: String): UserBookRating?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserRating(rating: UserBookRating)
    
    @Query("DELETE FROM user_book_ratings WHERE userId = :userId AND bookId = :bookId")
    suspend fun deleteUserRating(userId: String, bookId: String)
    
    @Query("SELECT AVG(rating) FROM user_book_ratings WHERE bookId = :bookId")
    suspend fun getAverageUserRatingForBook(bookId: String): Float?
}
