package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.components.BookCard
import com.iqraa.library.ui.viewmodel.BookViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryBooksScreen(
    categoryId: String,
    onBackClick: () -> Unit,
    onBookClick: (String) -> Unit,
    bookViewModel: BookViewModel = hiltViewModel()
) {
    val category by bookViewModel.getCategoryById(categoryId).collectAsState(initial = null)
    val booksInCategory by bookViewModel.getBooksByCategory(categoryId).collectAsState(initial = emptyList())

    LaunchedEffect(categoryId) {
        bookViewModel.selectCategory(categoryId)
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { 
                Text(category?.name ?: stringResource(R.string.books_in_category))
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            }
        )
        
        if (booksInCategory.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "لا توجد كتب في هذا التصنيف",
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "سيتم إضافة كتب جديدة قريباً",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(16.dp)
            ) {
                items(booksInCategory) { book ->
                    BookCard(
                        book = book,
                        onClick = { onBookClick(book.id) },
                        onFavoriteClick = { isFavorite ->
                            bookViewModel.toggleFavorite(book.id, isFavorite)
                        }
                    )
                }
            }
        }
    }
}
