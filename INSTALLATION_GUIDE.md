# دليل التثبيت والتشغيل - تطبيق مكتبة إقرأ

## 📋 المتطلبات الأساسية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, أو Linux Ubuntu 18.04+
- **ذاكرة الوصول العشوائي**: 8 GB أو أكثر (مُوصى به 16 GB)
- **مساحة التخزين**: 4 GB مساحة فارغة على الأقل
- **اتصال الإنترنت**: مطلوب لتحميل التبعيات

### البرامج المطلوبة
1. **Android Studio**: الإصدار Arctic Fox (2020.3.1) أو أحدث
2. **Java Development Kit (JDK)**: الإصدار 8 أو أحدث
3. **Git**: لاستنساخ المشروع

## 🔧 خطوات التثبيت التفصيلية

### الخطوة 1: تثبيت Android Studio

1. قم بتحميل Android Studio من [الموقع الرسمي](https://developer.android.com/studio)
2. قم بتثبيت البرنامج واتبع التعليمات
3. افتح Android Studio وأكمل الإعداد الأولي
4. تأكد من تثبيت Android SDK Platform 34

### الخطوة 2: تحميل المشروع

```bash
# استنساخ المشروع من GitHub
git clone https://github.com/your-username/iqraa-library.git

# الانتقال إلى مجلد المشروع
cd iqraa-library
```

### الخطوة 3: فتح المشروع في Android Studio

1. افتح Android Studio
2. اختر "Open an existing Android Studio project"
3. انتقل إلى مجلد المشروع واختره
4. انتظر حتى يتم تحميل المشروع وفهرسة الملفات

### الخطوة 4: تحميل الخطوط العربية

قم بتحميل الخطوط التالية ووضعها في `app/src/main/res/font/`:

#### خط أميري (Amiri)
- [تحميل من Google Fonts](https://fonts.google.com/specimen/Amiri)
- الملفات المطلوبة:
  - `amiri_regular.ttf`
  - `amiri_bold.ttf`

#### خط نوتو العربي (Noto Sans Arabic)
- [تحميل من Google Fonts](https://fonts.google.com/noto/specimen/Noto+Sans+Arabic)
- الملفات المطلوبة:
  - `noto_sans_arabic_regular.ttf`
  - `noto_sans_arabic_bold.ttf`

#### خط القاهرة (Cairo)
- [تحميل من Google Fonts](https://fonts.google.com/specimen/Cairo)
- الملفات المطلوبة:
  - `cairo_regular.ttf`
  - `cairo_bold.ttf`

### الخطوة 5: إنشاء الأيقونات

قم بإنشاء الأيقونات التالية في `app/src/main/res/drawable/`:

1. في Android Studio، انقر بزر الماوس الأيمن على مجلد `drawable`
2. اختر `New > Vector Asset`
3. اختر `Material Icon`
4. ابحث عن الأيقونة المطلوبة وأنشئها

الأيقونات المطلوبة:
- `ic_download` (Download icon)
- `ic_download_done` (Download done icon)
- `ic_error` (Error icon)
- `ic_book` (Book icon)
- `ic_new_book` (New releases icon)
- `ic_achievement` (Achievement icon)
- `ic_streak` (Streak icon)

### الخطوة 6: بناء المشروع

1. في Android Studio، اختر `Build > Make Project`
2. انتظر حتى يكتمل البناء
3. تأكد من عدم وجود أخطاء في البناء

### الخطوة 7: تشغيل التطبيق

#### على المحاكي (Emulator):
1. افتح AVD Manager من Android Studio
2. أنشئ جهاز افتراضي جديد أو استخدم موجود
3. تأكد من أن API Level 24 أو أحدث
4. شغل المحاكي
5. انقر على زر "Run" في Android Studio

#### على جهاز حقيقي:
1. فعل "Developer Options" على جهازك
2. فعل "USB Debugging"
3. وصل الجهاز بالكمبيوتر عبر USB
4. انقر على زر "Run" في Android Studio

## 🐛 حل المشاكل الشائعة

### مشكلة: فشل في بناء المشروع
**الحل:**
```bash
# نظف المشروع
./gradlew clean

# أعد بناء المشروع
./gradlew build
```

### مشكلة: الخطوط لا تظهر بشكل صحيح
**الحل:**
1. تأكد من وضع ملفات الخطوط في المجلد الصحيح
2. تأكد من أن أسماء الملفات صحيحة
3. أعد بناء المشروع

### مشكلة: أخطاء في Hilt/Dagger
**الحل:**
```bash
# نظف المشروع وأعد البناء
./gradlew clean build

# أو في Android Studio
Build > Clean Project
Build > Rebuild Project
```

### مشكلة: أخطاء في Room Database
**الحل:**
1. تأكد من أن جميع Entity classes محددة بشكل صحيح
2. تأكد من أن Database class يحتوي على جميع الـ DAOs
3. امسح بيانات التطبيق إذا كنت تختبر على جهاز

## 📱 اختبار التطبيق

### الاختبارات الأساسية
1. **الشاشة الرئيسية**: تأكد من ظهور الكتب والتصنيفات
2. **البحث**: جرب البحث عن كتاب
3. **المكتبة**: تحقق من عمل التبويبات
4. **الإعدادات**: جرب تغيير الخط والسمة
5. **القارئ**: افتح كتاب وجرب ميزات القراءة

### اختبارات متقدمة
1. **التحميل**: جرب تحميل كتاب (إذا كان متاحاً)
2. **الملاحظات**: أضف ملاحظة أو علامة مرجعية
3. **المراجعات**: اكتب مراجعة لكتاب
4. **المشاركة**: جرب مشاركة اقتباس

## 🔄 التحديثات

### تحديث التبعيات
```bash
# تحديث Gradle Wrapper
./gradlew wrapper --gradle-version=8.1.2

# تحديث التبعيات
./gradlew dependencyUpdates
```

### تحديث Android SDK
1. افتح SDK Manager في Android Studio
2. تحقق من التحديثات المتاحة
3. حمل وثبت التحديثات المطلوبة

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من الوثائق**: راجع README.md
2. **ابحث في Issues**: تحقق من GitHub Issues
3. **أنشئ Issue جديد**: إذا لم تجد حلاً
4. **تواصل معنا**: <EMAIL>

## 📝 ملاحظات مهمة

- تأكد من أن جهازك يدعم Android API Level 24 أو أحدث
- بعض الميزات قد تتطلب اتصال بالإنترنت
- الخطوط العربية ضرورية لعرض النصوص بشكل صحيح
- قم بحفظ عملك بانتظام أثناء التطوير

## 🎯 الخطوات التالية

بعد التثبيت الناجح:

1. استكشف التطبيق وميزاته
2. اقرأ الكود لفهم البنية
3. جرب إضافة ميزات جديدة
4. شارك في تطوير المشروع
5. اكتب اختبارات إضافية

---

**مبروك! 🎉 لقد نجحت في تثبيت وتشغيل تطبيق مكتبة إقرأ**
