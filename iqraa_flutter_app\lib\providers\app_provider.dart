import 'package:flutter/material.dart';
import '../models/book.dart';
import '../models/user.dart';

class AppProvider with ChangeNotifier {
  // User data
  User _currentUser = User(
    id: '1',
    name: 'أحم<PERSON> محمد',
    email: '<EMAIL>',
    booksRead: 12,
    readingGoal: 24,
    totalReadingTime: 2850, // 47.5 hours
    currentStreak: 7,
    favoriteGenres: ['الأدب العربي', 'الروايات', 'التاريخ'],
  );

  // Books data
  List<Book> _allBooks = Book.getSampleBooks();
  List<Book> _searchResults = [];
  String _searchQuery = '';
  String _selectedCategory = 'الكل';

  // UI state
  bool _isLoading = false;
  int _currentPageIndex = 0;

  // Getters
  User get currentUser => _currentUser;
  List<Book> get allBooks => _allBooks;
  List<Book> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  bool get isLoading => _isLoading;
  int get currentPageIndex => _currentPageIndex;

  // Filtered books
  List<Book> get favoriteBooks => _allBooks.where((book) => book.isFavorite).toList();
  List<Book> get downloadedBooks => _allBooks.where((book) => book.isDownloaded).toList();
  List<Book> get currentlyReading => _allBooks.where((book) => book.readingProgress > 0 && book.readingProgress < 1.0).toList();
  List<Book> get completedBooks => _allBooks.where((book) => book.readingProgress >= 1.0).toList();
  List<Book> get recentBooks => _allBooks.take(6).toList();

  // Categories
  List<String> get categories => [
    'الكل',
    'الأدب العربي',
    'الروايات',
    'التاريخ',
    'السيرة الذاتية',
    'الأدب الكلاسيكي',
  ];

  List<Book> get booksByCategory {
    if (_selectedCategory == 'الكل') return _allBooks;
    return _allBooks.where((book) => book.category == _selectedCategory).toList();
  }

  // Methods
  void setCurrentPageIndex(int index) {
    _currentPageIndex = index;
    notifyListeners();
  }

  void toggleFavorite(String bookId) {
    final bookIndex = _allBooks.indexWhere((book) => book.id == bookId);
    if (bookIndex != -1) {
      _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
        isFavorite: !_allBooks[bookIndex].isFavorite,
      );
      notifyListeners();
    }
  }

  void updateReadingProgress(String bookId, double progress) {
    final bookIndex = _allBooks.indexWhere((book) => book.id == bookId);
    if (bookIndex != -1) {
      _allBooks[bookIndex] = _allBooks[bookIndex].copyWith(
        readingProgress: progress,
        lastReadDate: DateTime.now(),
      );
      
      // Update user stats if book is completed
      if (progress >= 1.0 && _allBooks[bookIndex].readingProgress < 1.0) {
        _currentUser = _currentUser.copyWith(
          booksRead: _currentUser.booksRead + 1,
        );
      }
      
      notifyListeners();
    }
  }

  void searchBooks(String query) {
    _searchQuery = query;
    if (query.isEmpty) {
      _searchResults = [];
    } else {
      _searchResults = _allBooks.where((book) {
        return book.title.toLowerCase().contains(query.toLowerCase()) ||
               book.author.toLowerCase().contains(query.toLowerCase()) ||
               book.category.toLowerCase().contains(query.toLowerCase()) ||
               book.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
      }).toList();
    }
    notifyListeners();
  }

  void selectCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void updateUserPreferences(UserPreferences preferences) {
    _currentUser = _currentUser.copyWith(preferences: preferences);
    notifyListeners();
  }

  void updateReadingGoal(int goal) {
    _currentUser = _currentUser.copyWith(readingGoal: goal);
    notifyListeners();
  }

  void addReadingTime(int minutes) {
    _currentUser = _currentUser.copyWith(
      totalReadingTime: _currentUser.totalReadingTime + minutes,
    );
    notifyListeners();
  }

  Book? getBookById(String id) {
    try {
      return _allBooks.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Statistics
  Map<String, int> get readingStats {
    final stats = <String, int>{};
    for (final book in _allBooks) {
      final category = book.category;
      if (book.readingProgress > 0) {
        stats[category] = (stats[category] ?? 0) + 1;
      }
    }
    return stats;
  }

  List<Book> get recommendedBooks {
    // Simple recommendation based on favorite genres and high ratings
    return _allBooks.where((book) {
      return _currentUser.favoriteGenres.contains(book.category) && 
             book.rating >= 4.0 && 
             !book.isFavorite;
    }).take(5).toList();
  }
}
