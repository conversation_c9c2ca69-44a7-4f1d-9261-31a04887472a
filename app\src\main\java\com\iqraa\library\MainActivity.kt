package com.iqraa.library

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.LayoutDirection
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.ui.navigation.IqraaNavigation
import com.iqraa.library.ui.theme.IqraaLibraryTheme
import com.iqraa.library.ui.viewmodel.UserViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            val userViewModel: UserViewModel = hiltViewModel()
            val userPreferences by userViewModel.userPreferences.collectAsState()
            
            // Set RTL layout direction
            CompositionLocalProvider(
                LocalLayoutDirection provides if (userPreferences?.isRTL == true) {
                    LayoutDirection.Rtl
                } else {
                    LayoutDirection.Ltr
                }
            ) {
                IqraaLibraryTheme(
                    darkTheme = userPreferences?.isDarkMode ?: false
                ) {
                    Surface(
                        modifier = Modifier.fillMaxSize(),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        IqraaNavigation()
                    }
                }
            }
        }
    }
}
