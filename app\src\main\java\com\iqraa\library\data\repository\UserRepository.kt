package com.iqraa.library.data.repository

import com.iqraa.library.data.database.UserDao
import com.iqraa.library.data.model.User
import com.iqraa.library.data.model.UserPreferences
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val userDao: UserDao
) {
    
    suspend fun getUserById(userId: String): User? = userDao.getUserById(userId)
    
    fun getUserByIdFlow(userId: String): Flow<User?> = userDao.getUserByIdFlow(userId)
    
    suspend fun getUserByEmail(email: String): User? = userDao.getUserByEmail(email)
    
    suspend fun insertUser(user: User) = userDao.insertUser(user)
    
    suspend fun updateUser(user: User) = userDao.updateUser(user)
    
    suspend fun deleteUser(user: User) = userDao.deleteUser(user)
    
    suspend fun incrementBooksRead(userId: String) = userDao.incrementBooksRead(userId)
    
    suspend fun addReadingTime(userId: String, minutes: Long) = userDao.addReadingTime(userId, minutes)
    
    // User Preferences
    suspend fun getUserPreferences(userId: String): UserPreferences? = 
        userDao.getUserPreferences(userId)
    
    fun getUserPreferencesFlow(userId: String): Flow<UserPreferences?> = 
        userDao.getUserPreferencesFlow(userId)
    
    suspend fun insertUserPreferences(preferences: UserPreferences) = 
        userDao.insertUserPreferences(preferences)
    
    suspend fun updateUserPreferences(preferences: UserPreferences) = 
        userDao.updateUserPreferences(preferences)
    
    suspend fun updateThemeMode(userId: String, isDarkMode: Boolean) = 
        userDao.updateThemeMode(userId, isDarkMode)
    
    suspend fun updateFontSize(userId: String, fontSize: Float) = 
        userDao.updateFontSize(userId, fontSize)
    
    suspend fun updateFontFamily(userId: String, fontFamily: String) = 
        userDao.updateFontFamily(userId, fontFamily)
    
    suspend fun createDefaultPreferences(userId: String): UserPreferences {
        val defaultPreferences = UserPreferences(userId = userId)
        insertUserPreferences(defaultPreferences)
        return defaultPreferences
    }
}
