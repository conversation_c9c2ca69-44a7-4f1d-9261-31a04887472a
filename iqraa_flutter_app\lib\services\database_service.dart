import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../models/book.dart';

class DatabaseService {
  static Database? _database;
  static const String _databaseName = 'iqraa_books.db';
  static const int _databaseVersion = 1;

  // Table names
  static const String _booksTable = 'books';
  static const String _categoriesTable = 'categories';
  static const String _readingProgressTable = 'reading_progress';

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // Create tables
  Future<void> _onCreate(Database db, int version) async {
    // Categories table
    await db.execute('''
      CREATE TABLE $_categoriesTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        name_ar TEXT NOT NULL,
        description TEXT,
        icon_name TEXT,
        color_hex TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Books table
    await db.execute('''
      CREATE TABLE $_booksTable (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        title_ar TEXT,
        author TEXT NOT NULL,
        author_ar TEXT,
        description TEXT,
        category_id INTEGER,
        language TEXT DEFAULT 'العربية',
        page_count INTEGER,
        publish_date TEXT,
        rating REAL DEFAULT 0.0,
        review_count INTEGER DEFAULT 0,
        cover_url TEXT,
        local_cover_path TEXT,
        file_url TEXT,
        local_file_path TEXT,
        has_local_file INTEGER DEFAULT 0,
        file_size_mb REAL,
        is_favorite INTEGER DEFAULT 0,
        is_downloaded INTEGER DEFAULT 0,
        is_preloaded INTEGER DEFAULT 0,
        reading_progress REAL DEFAULT 0.0,
        tags TEXT,
        added_date TEXT DEFAULT CURRENT_TIMESTAMP,
        last_read_date TEXT,
        FOREIGN KEY (category_id) REFERENCES $_categoriesTable (id)
      )
    ''');

    // Reading progress table
    await db.execute('''
      CREATE TABLE $_readingProgressTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        book_id TEXT NOT NULL,
        current_page INTEGER DEFAULT 1,
        total_pages INTEGER DEFAULT 0,
        progress_percentage REAL DEFAULT 0.0,
        reading_time_minutes INTEGER DEFAULT 0,
        last_position TEXT,
        bookmarks TEXT,
        notes TEXT,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (book_id) REFERENCES $_booksTable (id)
      )
    ''');

    // Insert default categories
    await _insertDefaultCategories(db);
    
    // Insert preloaded books
    await _insertPreloadedBooks(db);
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle future database schema changes
  }

  // Insert default categories
  Future<void> _insertDefaultCategories(Database db) async {
    final categories = [
      {
        'name': 'islamic',
        'name_ar': 'الكتب الإسلامية',
        'description': 'القرآن الكريم والحديث الشريف والكتب الدينية',
        'icon_name': 'mosque',
        'color_hex': '#2E7D32'
      },
      {
        'name': 'novels',
        'name_ar': 'الروايات',
        'description': 'الروايات العربية والعالمية المترجمة',
        'icon_name': 'book',
        'color_hex': '#1976D2'
      },
      {
        'name': 'history',
        'name_ar': 'التاريخ',
        'description': 'كتب التاريخ الإسلامي والعربي والعالمي',
        'icon_name': 'history_edu',
        'color_hex': '#F57C00'
      },
      {
        'name': 'misc',
        'name_ar': 'منوعات',
        'description': 'كتب متنوعة في مختلف المجالات',
        'icon_name': 'library_books',
        'color_hex': '#7B1FA2'
      }
    ];

    for (var category in categories) {
      await db.insert(_categoriesTable, category);
    }
  }

  // Insert preloaded books
  Future<void> _insertPreloadedBooks(Database db) async {
    final books = [
      // Islamic Books
      {
        'id': 'quran_001',
        'title': 'القرآن الكريم',
        'title_ar': 'القرآن الكريم',
        'author': 'كلام الله تعالى',
        'author_ar': 'كلام الله تعالى',
        'description': 'المصحف الشريف كاملاً برواية حفص عن عاصم',
        'category_id': 1,
        'language': 'العربية',
        'page_count': 604,
        'publish_date': 'منذ 1400 سنة',
        'rating': 5.0,
        'review_count': 1000000,
        'local_cover_path': 'assets/covers/islamic/quran.jpg',
        'local_file_path': 'assets/books/islamic/quran.pdf',
        'has_local_file': 1,
        'file_size_mb': 15.5,
        'is_preloaded': 1,
        'tags': 'قرآن,إسلام,دين'
      },
      {
        'id': 'bukhari_001',
        'title': 'صحيح البخاري',
        'title_ar': 'صحيح البخاري',
        'author': 'الإمام البخاري',
        'author_ar': 'الإمام البخاري',
        'description': 'أصح كتاب بعد كتاب الله تعالى',
        'category_id': 1,
        'language': 'العربية',
        'page_count': 2000,
        'publish_date': '846 م',
        'rating': 4.9,
        'review_count': 50000,
        'local_cover_path': 'assets/covers/islamic/bukhari.jpg',
        'local_file_path': 'assets/books/islamic/bukhari.pdf',
        'has_local_file': 1,
        'file_size_mb': 45.2,
        'is_preloaded': 1,
        'tags': 'حديث,سنة,إسلام'
      },
      {
        'id': 'riyadh_001',
        'title': 'رياض الصالحين',
        'title_ar': 'رياض الصالحين',
        'author': 'الإمام النووي',
        'author_ar': 'الإمام النووي',
        'description': 'مجموعة من الأحاديث النبوية الشريفة مرتبة حسب الموضوعات',
        'category_id': 1,
        'language': 'العربية',
        'page_count': 800,
        'publish_date': '1277 م',
        'rating': 4.8,
        'review_count': 25000,
        'local_cover_path': 'assets/covers/islamic/riyadh.jpg',
        'local_file_path': 'assets/books/islamic/riyadh.pdf',
        'has_local_file': 1,
        'file_size_mb': 12.8,
        'is_preloaded': 1,
        'tags': 'حديث,أخلاق,تربية'
      },

      // Novels
      {
        'id': 'cities_salt_001',
        'title': 'مدن الملح',
        'title_ar': 'مدن الملح',
        'author': 'عبد الرحمن منيف',
        'author_ar': 'عبد الرحمن منيف',
        'description': 'خماسية روائية تصور التحولات الاجتماعية في المنطقة العربية',
        'category_id': 2,
        'language': 'العربية',
        'page_count': 650,
        'publish_date': '1984',
        'rating': 4.6,
        'review_count': 15000,
        'local_cover_path': 'assets/covers/novels/cities_salt.jpg',
        'local_file_path': 'assets/books/novels/cities_salt.pdf',
        'has_local_file': 1,
        'file_size_mb': 8.5,
        'is_preloaded': 1,
        'tags': 'رواية,عربي,اجتماعي'
      },
      {
        'id': 'black_suits_001',
        'title': 'الأسود يليق بك',
        'title_ar': 'الأسود يليق بك',
        'author': 'أحلام مستغانمي',
        'author_ar': 'أحلام مستغانمي',
        'description': 'رواية عربية معاصرة تتناول قصة حب في إطار اجتماعي متغير',
        'category_id': 2,
        'language': 'العربية',
        'page_count': 350,
        'publish_date': '2012',
        'rating': 4.2,
        'review_count': 12000,
        'local_cover_path': 'assets/covers/novels/black_suits.jpg',
        'local_file_path': 'assets/books/novels/black_suits.pdf',
        'has_local_file': 1,
        'file_size_mb': 5.2,
        'is_preloaded': 1,
        'tags': 'رواية,حب,معاصر'
      },

      // History
      {
        'id': 'tabari_001',
        'title': 'تاريخ الطبري',
        'title_ar': 'تاريخ الطبري',
        'author': 'محمد بن جرير الطبري',
        'author_ar': 'محمد بن جرير الطبري',
        'description': 'كتاب تاريخي شامل يغطي تاريخ العالم من بداية الخلق',
        'category_id': 3,
        'language': 'العربية',
        'page_count': 2800,
        'publish_date': '915 م',
        'rating': 4.8,
        'review_count': 8000,
        'local_cover_path': 'assets/covers/history/tabari.jpg',
        'local_file_path': 'assets/books/history/tabari.pdf',
        'has_local_file': 1,
        'file_size_mb': 35.0,
        'is_preloaded': 1,
        'tags': 'تاريخ,إسلامي,مرجع'
      },
      {
        'id': 'ibn_khaldun_001',
        'title': 'مقدمة ابن خلدون',
        'title_ar': 'مقدمة ابن خلدون',
        'author': 'عبد الرحمن بن خلدون',
        'author_ar': 'عبد الرحمن بن خلدون',
        'description': 'مقدمة في علم الاجتماع والتاريخ والحضارة',
        'category_id': 3,
        'language': 'العربية',
        'page_count': 1200,
        'publish_date': '1377 م',
        'rating': 4.7,
        'review_count': 6000,
        'local_cover_path': 'assets/covers/history/ibn_khaldun.jpg',
        'local_file_path': 'assets/books/history/ibn_khaldun.pdf',
        'has_local_file': 1,
        'file_size_mb': 18.5,
        'is_preloaded': 1,
        'tags': 'تاريخ,اجتماع,فلسفة'
      },

      // Miscellaneous
      {
        'id': 'kalila_001',
        'title': 'كليلة ودمنة',
        'title_ar': 'كليلة ودمنة',
        'author': 'ابن المقفع',
        'author_ar': 'ابن المقفع',
        'description': 'مجموعة من الحكايات والأمثال على ألسنة الحيوانات',
        'category_id': 4,
        'language': 'العربية',
        'page_count': 280,
        'publish_date': '750 م',
        'rating': 4.4,
        'review_count': 4000,
        'local_cover_path': 'assets/covers/misc/kalila.jpg',
        'local_file_path': 'assets/books/misc/kalila.pdf',
        'has_local_file': 1,
        'file_size_mb': 4.8,
        'is_preloaded': 1,
        'tags': 'حكايات,تراث,حكمة'
      },
      {
        'id': 'ayyam_001',
        'title': 'الأيام',
        'title_ar': 'الأيام',
        'author': 'طه حسين',
        'author_ar': 'طه حسين',
        'description': 'سيرة ذاتية لعميد الأدب العربي طه حسين',
        'category_id': 4,
        'language': 'العربية',
        'page_count': 320,
        'publish_date': '1929',
        'rating': 4.7,
        'review_count': 7000,
        'local_cover_path': 'assets/covers/misc/ayyam.jpg',
        'local_file_path': 'assets/books/misc/ayyam.pdf',
        'has_local_file': 1,
        'file_size_mb': 6.2,
        'is_preloaded': 1,
        'tags': 'سيرة,تعليم,إلهام'
      }
    ];

    for (var book in books) {
      await db.insert(_booksTable, book);
    }
  }

  // Get all books
  Future<List<Book>> getAllBooks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(_booksTable);
    return List.generate(maps.length, (i) => _mapToBook(maps[i]));
  }

  // Get books by category
  Future<List<Book>> getBooksByCategory(String categoryName) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT b.* FROM $_booksTable b
      JOIN $_categoriesTable c ON b.category_id = c.id
      WHERE c.name = ?
    ''', [categoryName]);
    return List.generate(maps.length, (i) => _mapToBook(maps[i]));
  }

  // Get favorite books
  Future<List<Book>> getFavoriteBooks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _booksTable,
      where: 'is_favorite = ?',
      whereArgs: [1],
    );
    return List.generate(maps.length, (i) => _mapToBook(maps[i]));
  }

  // Search books
  Future<List<Book>> searchBooks(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      _booksTable,
      where: 'title LIKE ? OR author LIKE ? OR description LIKE ? OR tags LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%', '%$query%'],
    );
    return List.generate(maps.length, (i) => _mapToBook(maps[i]));
  }

  // Insert new book
  Future<void> insertBook(Book book) async {
    final db = await database;
    await db.insert(_booksTable, _bookToMap(book));
  }

  // Update book
  Future<void> updateBook(Book book) async {
    final db = await database;
    await db.update(
      _booksTable,
      _bookToMap(book),
      where: 'id = ?',
      whereArgs: [book.id],
    );
  }

  // Update reading progress
  Future<void> updateReadingProgress(String bookId, double progress) async {
    final db = await database;
    await db.update(
      _booksTable,
      {'reading_progress': progress, 'last_read_date': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // Toggle favorite
  Future<void> toggleFavorite(String bookId, bool isFavorite) async {
    final db = await database;
    await db.update(
      _booksTable,
      {'is_favorite': isFavorite ? 1 : 0},
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }

  // Get categories
  Future<List<Map<String, dynamic>>> getCategories() async {
    final db = await database;
    return await db.query(_categoriesTable);
  }

  // Convert database map to Book object
  Book _mapToBook(Map<String, dynamic> map) {
    return Book(
      id: map['id'],
      title: map['title'],
      author: map['author'],
      coverUrl: map['cover_url'] ?? map['local_cover_path'] ?? '',
      rating: map['rating']?.toDouble() ?? 0.0,
      reviewCount: map['review_count'] ?? 0,
      category: _getCategoryName(map['category_id']),
      description: map['description'],
      pageCount: map['page_count'],
      publishDate: map['publish_date'],
      isFavorite: map['is_favorite'] == 1,
      isDownloaded: map['is_downloaded'] == 1,
      readingProgress: map['reading_progress']?.toDouble() ?? 0.0,
      fileUrl: map['file_url'],
      localFilePath: map['local_file_path'],
      language: map['language'] ?? 'العربية',
      tags: map['tags']?.split(',') ?? [],
      addedDate: DateTime.tryParse(map['added_date'] ?? '') ?? DateTime.now(),
      lastReadDate: map['last_read_date'] != null 
          ? DateTime.tryParse(map['last_read_date']) 
          : null,
      hasLocalFile: map['has_local_file'] == 1,
      fileSizeInMB: map['file_size_mb']?.toDouble(),
    );
  }

  // Convert Book object to database map
  Map<String, dynamic> _bookToMap(Book book) {
    return {
      'id': book.id,
      'title': book.title,
      'author': book.author,
      'description': book.description,
      'category_id': _getCategoryId(book.category),
      'language': book.language,
      'page_count': book.pageCount,
      'publish_date': book.publishDate,
      'rating': book.rating,
      'review_count': book.reviewCount,
      'cover_url': book.coverUrl,
      'local_cover_path': book.coverUrl,
      'file_url': book.fileUrl,
      'local_file_path': book.localFilePath,
      'has_local_file': book.hasLocalFile ? 1 : 0,
      'file_size_mb': book.fileSizeInMB,
      'is_favorite': book.isFavorite ? 1 : 0,
      'is_downloaded': book.isDownloaded ? 1 : 0,
      'reading_progress': book.readingProgress,
      'tags': book.tags.join(','),
      'added_date': book.addedDate.toIso8601String(),
      'last_read_date': book.lastReadDate?.toIso8601String(),
    };
  }

  // Helper methods for category mapping
  String _getCategoryName(int? categoryId) {
    switch (categoryId) {
      case 1: return 'الكتب الإسلامية';
      case 2: return 'الروايات';
      case 3: return 'التاريخ';
      case 4: return 'منوعات';
      default: return 'منوعات';
    }
  }

  int _getCategoryId(String categoryName) {
    switch (categoryName) {
      case 'الكتب الإسلامية': return 1;
      case 'الروايات': return 2;
      case 'التاريخ': return 3;
      case 'منوعات': return 4;
      default: return 4;
    }
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
