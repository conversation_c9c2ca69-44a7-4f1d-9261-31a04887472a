package com.iqraa.library.data.database

import androidx.room.*
import com.iqraa.library.data.model.Book
import com.iqraa.library.data.model.ReadingStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface BookDao {
    
    @Query("SELECT * FROM books ORDER BY addedToLibraryDate DESC")
    fun getAllBooks(): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE categoryId = :categoryId ORDER BY title ASC")
    fun getBooksByCategory(categoryId: String): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE isFavorite = 1 ORDER BY addedToLibraryDate DESC")
    fun getFavoriteBooks(): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE isDownloaded = 1 ORDER BY addedToLibraryDate DESC")
    fun getDownloadedBooks(): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE readingProgress > 0 AND readingProgress < 1 ORDER BY addedToLibraryDate DESC")
    fun getCurrentlyReadingBooks(): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE readingProgress = 1 ORDER BY addedToLibraryDate DESC")
    fun getCompletedBooks(): Flow<List<Book>>
    
    @Query("""
        SELECT * FROM books 
        WHERE title LIKE '%' || :query || '%' 
        OR author LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        ORDER BY title ASC
    """)
    fun searchBooks(query: String): Flow<List<Book>>
    
    @Query("SELECT * FROM books WHERE id = :bookId")
    suspend fun getBookById(bookId: String): Book?
    
    @Query("SELECT * FROM books WHERE id = :bookId")
    fun getBookByIdFlow(bookId: String): Flow<Book?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBook(book: Book)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBooks(books: List<Book>)
    
    @Update
    suspend fun updateBook(book: Book)
    
    @Delete
    suspend fun deleteBook(book: Book)
    
    @Query("UPDATE books SET isFavorite = :isFavorite WHERE id = :bookId")
    suspend fun updateFavoriteStatus(bookId: String, isFavorite: Boolean)
    
    @Query("UPDATE books SET isDownloaded = :isDownloaded, downloadPath = :downloadPath WHERE id = :bookId")
    suspend fun updateDownloadStatus(bookId: String, isDownloaded: Boolean, downloadPath: String?)
    
    @Query("UPDATE books SET lastReadPage = :page, readingProgress = :progress WHERE id = :bookId")
    suspend fun updateReadingProgress(bookId: String, page: Int, progress: Float)
    
    @Query("SELECT DISTINCT categoryName FROM books ORDER BY categoryName ASC")
    fun getAllCategories(): Flow<List<String>>
    
    @Query("SELECT COUNT(*) FROM books")
    suspend fun getBooksCount(): Int
    
    @Query("SELECT COUNT(*) FROM books WHERE isDownloaded = 1")
    suspend fun getDownloadedBooksCount(): Int
}
