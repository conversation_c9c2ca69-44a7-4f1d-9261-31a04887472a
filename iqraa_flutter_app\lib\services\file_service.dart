import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class FileService {
  static const String _booksFolder = 'books';

  // Get the books directory
  static Future<Directory> getBooksDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final booksDir = Directory(path.join(appDir.path, _booksFolder));
    
    if (!await booksDir.exists()) {
      await booksDir.create(recursive: true);
    }
    
    return booksDir;
  }

  // Pick a PDF file
  static Future<File?> pickPdfFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        return File(result.files.single.path!);
      }
      
      return null;
    } catch (e) {
      print('Error picking PDF file: $e');
      return null;
    }
  }

  // Save PDF file to app directory
  static Future<String?> savePdfFile(File pdfFile, String bookId) async {
    try {
      final booksDir = await getBooksDirectory();
      final fileName = '${bookId}_${path.basename(pdfFile.path)}';
      final savedFile = File(path.join(booksDir.path, fileName));
      
      await pdfFile.copy(savedFile.path);
      return savedFile.path;
    } catch (e) {
      print('Error saving PDF file: $e');
      return null;
    }
  }

  // Delete PDF file
  static Future<bool> deletePdfFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting PDF file: $e');
      return false;
    }
  }

  // Get all saved PDF files
  static Future<List<File>> getAllPdfFiles() async {
    try {
      final booksDir = await getBooksDirectory();
      final files = await booksDir.list().toList();
      
      return files
          .whereType<File>()
          .where((file) => file.path.toLowerCase().endsWith('.pdf'))
          .toList();
    } catch (e) {
      print('Error getting PDF files: $e');
      return [];
    }
  }

  // Check if file exists
  static Future<bool> fileExists(String filePath) async {
    try {
      return await File(filePath).exists();
    } catch (e) {
      return false;
    }
  }

  // Get file size in MB
  static Future<double> getFileSize(String filePath) async {
    try {
      final file = File(filePath);
      final bytes = await file.length();
      return bytes / (1024 * 1024); // Convert to MB
    } catch (e) {
      return 0.0;
    }
  }

  // Generate thumbnail for PDF (placeholder for now)
  static Future<String?> generatePdfThumbnail(String pdfPath) async {
    // This would require additional libraries like pdf_render
    // For now, return a placeholder
    return null;
  }
}
