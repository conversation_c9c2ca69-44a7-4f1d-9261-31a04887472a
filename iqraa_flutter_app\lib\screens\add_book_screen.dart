import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/book.dart';
import '../providers/offline_app_provider.dart';
// import '../utils/app_theme.dart';
// import '../services/file_service.dart';

class AddBookScreen extends StatefulWidget {
  const AddBookScreen({super.key});

  @override
  State<AddBookScreen> createState() => _AddBookScreenState();
}

class _AddBookScreenState extends State<AddBookScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _authorController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _coverUrlController = TextEditingController();
  final _pageCountController = TextEditingController();
  final _publishDateController = TextEditingController();

  String _selectedCategory = 'الأدب العربي';
  String _selectedLanguage = 'العربية';
  double _rating = 4.0;
  List<String> _tags = [];
  final _tagController = TextEditingController();

  // PDF file handling
  File? _selectedPdfFile;
  String? _pdfFileName;
  double? _pdfFileSize;
  bool _isUploadingPdf = false;

  @override
  void dispose() {
    _titleController.dispose();
    _authorController.dispose();
    _descriptionController.dispose();
    _coverUrlController.dispose();
    _pageCountController.dispose();
    _publishDateController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة كتاب جديد'),
        actions: [
          TextButton(
            onPressed: _saveBook,
            child: const Text(
              'حفظ',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Basic Info Section
              _buildSectionTitle('المعلومات الأساسية'),
              const SizedBox(height: 16),

              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان الكتاب *',
                  hintText: 'أدخل عنوان الكتاب',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عنوان الكتاب';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              TextFormField(
                controller: _authorController,
                decoration: const InputDecoration(
                  labelText: 'اسم المؤلف *',
                  hintText: 'أدخل اسم المؤلف',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم المؤلف';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف الكتاب',
                  hintText: 'أدخل وصفاً مختصراً للكتاب',
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // Category and Language Section
              _buildSectionTitle('التصنيف واللغة'),
              const SizedBox(height: 16),

              Consumer<OfflineAppProvider>(
                builder: (context, provider, child) {
                  return DropdownButtonFormField<String>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'التصنيف',
                    ),
                    items: provider.categories
                        .where((cat) => cat != 'الكل')
                        .map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCategory = value!;
                      });
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              DropdownButtonFormField<String>(
                value: _selectedLanguage,
                decoration: const InputDecoration(
                  labelText: 'اللغة',
                ),
                items: const [
                  DropdownMenuItem(value: 'العربية', child: Text('العربية')),
                  DropdownMenuItem(value: 'مترجم', child: Text('مترجم')),
                  DropdownMenuItem(
                      value: 'الإنجليزية', child: Text('الإنجليزية')),
                  DropdownMenuItem(value: 'الفرنسية', child: Text('الفرنسية')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedLanguage = value!;
                  });
                },
              ),

              const SizedBox(height: 24),

              // Additional Info Section
              _buildSectionTitle('معلومات إضافية'),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _pageCountController,
                      decoration: const InputDecoration(
                        labelText: 'عدد الصفحات',
                        hintText: '300',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _publishDateController,
                      decoration: const InputDecoration(
                        labelText: 'سنة النشر',
                        hintText: '2023',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              TextFormField(
                controller: _coverUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط صورة الغلاف',
                  hintText: 'https://example.com/cover.jpg',
                ),
              ),

              const SizedBox(height: 24),

              // Rating Section
              _buildSectionTitle('التقييم'),
              const SizedBox(height: 16),

              Row(
                children: [
                  Text(
                    'التقييم: ${_rating.toStringAsFixed(1)}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Slider(
                      value: _rating,
                      min: 1.0,
                      max: 5.0,
                      divisions: 8,
                      onChanged: (value) {
                        setState(() {
                          _rating = value;
                        });
                      },
                    ),
                  ),
                  Row(
                    children: List.generate(5, (index) {
                      return Icon(
                        index < _rating.floor()
                            ? Icons.star
                            : index < _rating
                                ? Icons.star_half
                                : Icons.star_border,
                        color: Colors.amber,
                        size: 20,
                      );
                    }),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Tags Section
              _buildSectionTitle('الكلمات المفتاحية'),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _tagController,
                      decoration: const InputDecoration(
                        labelText: 'إضافة كلمة مفتاحية',
                        hintText: 'مثل: رومانسي، تاريخي، إلخ',
                      ),
                      onFieldSubmitted: _addTag,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _addTag(_tagController.text),
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              if (_tags.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _tags.map((tag) {
                    return Chip(
                      label: Text(tag),
                      deleteIcon: const Icon(Icons.close, size: 18),
                      onDeleted: () {
                        setState(() {
                          _tags.remove(tag);
                        });
                      },
                      backgroundColor:
                          Theme.of(context).primaryColor.withOpacity(0.1),
                      labelStyle: TextStyle(
                        color: Theme.of(context).primaryColor,
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 24),
              ],

              // PDF Upload Section
              _buildSectionTitle('رفع ملف PDF'),
              const SizedBox(height: 16),

              GestureDetector(
                onTap: _isUploadingPdf ? null : _pickPdfFile,
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedPdfFile != null
                          ? Theme.of(context).primaryColor
                          : Colors.grey[300]!,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    color: _selectedPdfFile != null
                        ? Theme.of(context).primaryColor.withOpacity(0.05)
                        : Colors.grey[50],
                  ),
                  child: _selectedPdfFile == null
                      ? Column(
                          children: [
                            Icon(
                              Icons.cloud_upload,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'انقر لرفع ملف PDF',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'يمكنك رفع ملف PDF للكتاب لقراءته في التطبيق',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[500],
                                  ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.picture_as_pdf,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _pdfFileName ?? 'ملف PDF',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'الحجم: ${_pdfFileSize?.toStringAsFixed(1) ?? '0.0'} ميجابايت',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Colors.grey[600],
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: _removePdfFile,
                              icon: const Icon(Icons.close, color: Colors.red),
                            ),
                          ],
                        ),
                ),
              ),

              const SizedBox(height: 24),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _saveBook,
                  icon: const Icon(Icons.save),
                  label: const Text('حفظ الكتاب'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ),

              const SizedBox(height: 100), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
    );
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }

  Future<void> _pickPdfFile() async {
    setState(() {
      _isUploadingPdf = true;
    });

    try {
      // Simulate file picking for demo
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        setState(() {
          _selectedPdfFile = File('sample.pdf'); // Placeholder
          _pdfFileName = 'sample_book.pdf';
          _pdfFileSize = 5.2; // 5.2 MB
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم اختيار ملف PDF بنجاح (عينة تجريبية)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الملف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingPdf = false;
        });
      }
    }
  }

  void _removePdfFile() {
    setState(() {
      _selectedPdfFile = null;
      _pdfFileName = null;
      _pdfFileSize = null;
    });
  }

  Future<void> _saveBook() async {
    if (_formKey.currentState!.validate()) {
      String? localFilePath;

      // Simulate saving PDF file if selected
      if (_selectedPdfFile != null) {
        // Simulate file saving for demo
        await Future.delayed(const Duration(milliseconds: 500));
        localFilePath = '/demo/path/$_pdfFileName';
      }

      final newBook = Book(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text,
        author: _authorController.text,
        coverUrl: _coverUrlController.text.isNotEmpty
            ? _coverUrlController.text
            : 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=400&fit=crop',
        rating: _rating,
        reviewCount: 0,
        category: _selectedCategory,
        description: _descriptionController.text.isNotEmpty
            ? _descriptionController.text
            : null,
        pageCount: _pageCountController.text.isNotEmpty
            ? int.tryParse(_pageCountController.text)
            : null,
        publishDate: _publishDateController.text.isNotEmpty
            ? _publishDateController.text
            : null,
        language: _selectedLanguage,
        tags: _tags,
        localFilePath: localFilePath,
        hasLocalFile: localFilePath != null,
        fileSizeInMB: _pdfFileSize,
      );

      if (mounted) {
        // Add book to provider
        await context.read<OfflineAppProvider>().addBook(newBook);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إضافة الكتاب "${newBook.title}" بنجاح'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'عرض',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pop(context);
                // Navigate to book details
              },
            ),
          ),
        );

        Navigator.pop(context);
      }
    }
  }
}
