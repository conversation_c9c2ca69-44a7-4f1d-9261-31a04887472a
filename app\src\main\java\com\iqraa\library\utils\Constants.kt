package com.iqraa.library.utils

object Constants {
    
    // Database
    const val DATABASE_NAME = "iqraa_database"
    const val DATABASE_VERSION = 1
    
    // Preferences
    const val PREFERENCES_NAME = "iqraa_preferences"
    const val KEY_USER_ID = "user_id"
    const val KEY_FIRST_LAUNCH = "first_launch"
    const val KEY_THEME_MODE = "theme_mode"
    const val KEY_FONT_SIZE = "font_size"
    const val KEY_FONT_FAMILY = "font_family"
    const val KEY_RTL_MODE = "rtl_mode"
    
    // Default Values
    const val DEFAULT_FONT_SIZE = 16f
    const val DEFAULT_LINE_SPACING = 1.5f
    const val DEFAULT_FONT_FAMILY = "Amiri"
    const val DEFAULT_BACKGROUND_COLOR = "#FFFFFF"
    const val DEFAULT_TEXT_COLOR = "#000000"
    
    // File Types
    const val SUPPORTED_FILE_TYPES = "pdf,epub,mobi,txt"
    const val MAX_FILE_SIZE = 100 * 1024 * 1024 // 100 MB
    
    // Reading
    const val WORDS_PER_PAGE = 300
    const val AUTO_SAVE_INTERVAL = 30000L // 30 seconds
    const val READING_SESSION_TIMEOUT = 300000L // 5 minutes
    
    // Download
    const val DOWNLOAD_TIMEOUT = 60000L // 60 seconds
    const val MAX_CONCURRENT_DOWNLOADS = 3
    const val DOWNLOAD_RETRY_COUNT = 3
    
    // Search
    const val SEARCH_DEBOUNCE_TIME = 300L
    const val MIN_SEARCH_QUERY_LENGTH = 2
    const val MAX_SEARCH_RESULTS = 50
    
    // Categories
    val DEFAULT_CATEGORY_COLORS = listOf(
        "#2196F3", "#4CAF50", "#FF9800", "#9C27B0",
        "#F44336", "#607D8B", "#795548", "#E91E63",
        "#3F51B5", "#009688", "#CDDC39", "#FF5722"
    )
    
    // Highlight Colors
    val HIGHLIGHT_COLORS = listOf(
        "#FFFF00", // Yellow
        "#00FF00", // Green
        "#00FFFF", // Cyan
        "#FF00FF", // Magenta
        "#FFA500", // Orange
        "#FF69B4"  // Hot Pink
    )
    
    // API
    const val BASE_URL = "https://api.iqraa-library.com/"
    const val API_VERSION = "v1"
    const val API_TIMEOUT = 30000L
    
    // Pagination
    const val PAGE_SIZE = 20
    const val PREFETCH_DISTANCE = 5
    
    // Cache
    const val CACHE_SIZE = 10 * 1024 * 1024 // 10 MB
    const val CACHE_MAX_AGE = 7 * 24 * 60 * 60 // 7 days
    
    // Notifications
    const val NOTIFICATION_CHANNEL_ID = "iqraa_notifications"
    const val NOTIFICATION_CHANNEL_NAME = "Iqraa Library"
    const val NOTIFICATION_CHANNEL_DESCRIPTION = "Notifications for Iqraa Library app"
    
    // Analytics
    const val ANALYTICS_READING_SESSION = "reading_session"
    const val ANALYTICS_BOOK_DOWNLOAD = "book_download"
    const val ANALYTICS_BOOK_FAVORITE = "book_favorite"
    const val ANALYTICS_REVIEW_SUBMIT = "review_submit"
    const val ANALYTICS_SEARCH_QUERY = "search_query"
    
    // Error Messages
    const val ERROR_NETWORK = "خطأ في الاتصال بالشبكة"
    const val ERROR_FILE_NOT_FOUND = "الملف غير موجود"
    const val ERROR_INVALID_FILE_TYPE = "نوع الملف غير مدعوم"
    const val ERROR_FILE_TOO_LARGE = "حجم الملف كبير جداً"
    const val ERROR_DOWNLOAD_FAILED = "فشل في تحميل الملف"
    const val ERROR_UNKNOWN = "خطأ غير معروف"
    
    // Success Messages
    const val SUCCESS_BOOK_DOWNLOADED = "تم تحميل الكتاب بنجاح"
    const val SUCCESS_REVIEW_SUBMITTED = "تم إرسال المراجعة بنجاح"
    const val SUCCESS_NOTE_SAVED = "تم حفظ الملاحظة بنجاح"
    const val SUCCESS_BOOKMARK_ADDED = "تم إضافة العلامة المرجعية"
    
    // Date Formats
    const val DATE_FORMAT_DISPLAY = "dd/MM/yyyy"
    const val DATE_FORMAT_API = "yyyy-MM-dd'T'HH:mm:ss'Z'"
    const val TIME_FORMAT_DISPLAY = "HH:mm"
    
    // Regex Patterns
    const val EMAIL_PATTERN = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    const val PHONE_PATTERN = "^[+]?[0-9]{10,15}$"
    
    // Social Media
    const val TWITTER_SHARE_URL = "https://twitter.com/intent/tweet?text="
    const val FACEBOOK_SHARE_URL = "https://www.facebook.com/sharer/sharer.php?u="
    const val WHATSAPP_SHARE_URL = "https://wa.me/?text="
    
    // App Info
    const val APP_VERSION = "1.0.0"
    const val APP_BUILD = "1"
    const val PRIVACY_POLICY_URL = "https://iqraa-library.com/privacy"
    const val TERMS_OF_SERVICE_URL = "https://iqraa-library.com/terms"
    const val SUPPORT_EMAIL = "<EMAIL>"
    const val WEBSITE_URL = "https://iqraa-library.com"
}
