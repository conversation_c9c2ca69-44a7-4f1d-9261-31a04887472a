class User {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final int booksRead;
  final int readingGoal;
  final int totalReadingTime; // in minutes
  final int currentStreak;
  final DateTime joinDate;
  final List<String> favoriteGenres;
  final UserPreferences preferences;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    this.booksRead = 0,
    this.readingGoal = 12,
    this.totalReadingTime = 0,
    this.currentStreak = 0,
    DateTime? joinDate,
    this.favoriteGenres = const [],
    UserPreferences? preferences,
  }) : joinDate = joinDate ?? DateTime.now(),
       preferences = preferences ?? UserPreferences();

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? avatarUrl,
    int? booksRead,
    int? readingGoal,
    int? totalReadingTime,
    int? currentStreak,
    DateTime? joinDate,
    List<String>? favoriteGenres,
    UserPreferences? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      booksRead: booksRead ?? this.booksRead,
      readingGoal: readingGoal ?? this.readingGoal,
      totalReadingTime: totalReadingTime ?? this.totalReadingTime,
      currentStreak: currentStreak ?? this.currentStreak,
      joinDate: joinDate ?? this.joinDate,
      favoriteGenres: favoriteGenres ?? this.favoriteGenres,
      preferences: preferences ?? this.preferences,
    );
  }

  String get readingTimeFormatted {
    final hours = totalReadingTime ~/ 60;
    final minutes = totalReadingTime % 60;
    if (hours > 0) {
      return '${hours}س ${minutes}د';
    }
    return '${minutes}د';
  }

  double get goalProgress => booksRead / readingGoal;
}

class UserPreferences {
  final bool isDarkMode;
  final double fontSize;
  final String fontFamily;
  final bool notificationsEnabled;
  final bool autoNightMode;
  final String language;

  UserPreferences({
    this.isDarkMode = false,
    this.fontSize = 16.0,
    this.fontFamily = 'Cairo',
    this.notificationsEnabled = true,
    this.autoNightMode = false,
    this.language = 'ar',
  });

  UserPreferences copyWith({
    bool? isDarkMode,
    double? fontSize,
    String? fontFamily,
    bool? notificationsEnabled,
    bool? autoNightMode,
    String? language,
  }) {
    return UserPreferences(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      autoNightMode: autoNightMode ?? this.autoNightMode,
      language: language ?? this.language,
    );
  }
}
