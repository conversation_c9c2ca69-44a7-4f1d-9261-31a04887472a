import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../widgets/book_card.dart';
import '../utils/app_theme.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البحث والاستكشاف'),
      ),
      body: Consumer<AppProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Search Bar
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    TextField(
                      controller: _searchController,
                      focusNode: _searchFocusNode,
                      decoration: InputDecoration(
                        hintText: 'ابحث عن كتاب، مؤلف، أو تصنيف...',
                        prefixIcon: const Icon(Icons.search),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  provider.searchBooks('');
                                  setState(() {});
                                },
                              )
                            : null,
                      ),
                      onChanged: (value) {
                        provider.searchBooks(value);
                        setState(() {});
                      },
                    ),
                    
                    // Categories Filter
                    const SizedBox(height: 16),
                    SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: provider.categories.length,
                        itemBuilder: (context, index) {
                          final category = provider.categories[index];
                          final isSelected = provider.selectedCategory == category;
                          
                          return Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: FilterChip(
                              label: Text(category),
                              selected: isSelected,
                              onSelected: (selected) {
                                provider.selectCategory(category);
                              },
                              selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                              checkmarkColor: Theme.of(context).primaryColor,
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),

              // Search Results
              Expanded(
                child: _buildSearchContent(provider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchContent(AppProvider provider) {
    if (_searchController.text.isEmpty) {
      return _buildBrowseContent(provider);
    }

    if (provider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (provider.searchResults.isEmpty) {
      return _buildNoResults();
    }

    return _buildSearchResults(provider);
  }

  Widget _buildBrowseContent(AppProvider provider) {
    final booksByCategory = provider.booksByCategory;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Featured Section
          _buildFeaturedSection(provider),
          
          const SizedBox(height: 24),
          
          // Category Books
          if (provider.selectedCategory != 'الكل') ...[
            Row(
              children: [
                Icon(
                  Icons.category,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  provider.selectedCategory,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.6,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: booksByCategory.length,
              itemBuilder: (context, index) {
                return BookCard(
                  book: booksByCategory[index],
                  onTap: () => _openBookDetails(booksByCategory[index]),
                );
              },
            ),
          ] else ...[
            // All Books
            Text(
              'جميع الكتب',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.6,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: provider.allBooks.length,
              itemBuilder: (context, index) {
                return BookCard(
                  book: provider.allBooks[index],
                  onTap: () => _openBookDetails(provider.allBooks[index]),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFeaturedSection(AppProvider provider) {
    final recommendedBooks = provider.recommendedBooks;
    
    if (recommendedBooks.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.arabicGold.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.star,
                color: AppTheme.arabicGold,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'مقترح لك',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: recommendedBooks.length,
            itemBuilder: (context, index) {
              return BookCard(
                book: recommendedBooks[index],
                onTap: () => _openBookDetails(recommendedBooks[index]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(AppProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            'نتائج البحث (${provider.searchResults.length})',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            itemCount: provider.searchResults.length,
            itemBuilder: (context, index) {
              return BookCard(
                book: provider.searchResults[index],
                isHorizontal: true,
                onTap: () => _openBookDetails(provider.searchResults[index]),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNoResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد نتائج',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب البحث بكلمات مختلفة أو تصفح التصنيفات',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          OutlinedButton.icon(
            onPressed: () {
              _searchController.clear();
              context.read<AppProvider>().searchBooks('');
              context.read<AppProvider>().selectCategory('الكل');
              setState(() {});
            },
            icon: const Icon(Icons.refresh),
            label: const Text('مسح البحث'),
          ),
        ],
      ),
    );
  }

  void _openBookDetails(book) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح تفاصيل كتاب: ${book.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
