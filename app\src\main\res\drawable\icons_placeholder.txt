# Icons Directory

This directory should contain the following icon files:

## Notification Icons (24dp, white)
- ic_download.xml
- ic_download_done.xml
- ic_error.xml
- ic_book.xml
- ic_new_book.xml
- ic_achievement.xml
- ic_streak.xml

## App Icons
- ic_launcher.png (48dp, 72dp, 96dp, 144dp, 192dp)
- ic_launcher_round.png (48dp, 72dp, 96dp, 144dp, 192dp)

## Vector Drawables
You can create these as vector drawables using Android Studio:
1. Right-click on drawable folder
2. New > Vector Asset
3. Choose Material Icon
4. Search for the icon name
5. Customize color and size as needed

Example vector drawable (ic_download.xml):
```xml
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="?attr/colorOnSurface">
  <path
      android:fillColor="@android:color/white"
      android:pathData="M19,9h-4V3H9v6H5l7,7 7,-7zM5,18v2h14v-2H5z"/>
</vector>
```

After creating the icons, remove this placeholder file.
