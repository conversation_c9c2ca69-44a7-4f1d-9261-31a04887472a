class Book {
  final String id;
  final String title;
  final String author;
  final String coverUrl;
  final double rating;
  final String category;
  final String? description;
  final int? pageCount;
  final String? publishDate;
  final bool isFavorite;
  final bool isDownloaded;
  final double readingProgress;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.coverUrl,
    required this.rating,
    required this.category,
    this.description,
    this.pageCount,
    this.publishDate,
    this.isFavorite = false,
    this.isDownloaded = false,
    this.readingProgress = 0.0,
  });

  Book copyWith({
    String? id,
    String? title,
    String? author,
    String? coverUrl,
    double? rating,
    String? category,
    String? description,
    int? pageCount,
    String? publishDate,
    bool? isFavorite,
    bool? isDownloaded,
    double? readingProgress,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      coverUrl: coverUrl ?? this.coverUrl,
      rating: rating ?? this.rating,
      category: category ?? this.category,
      description: description ?? this.description,
      pageCount: pageCount ?? this.pageCount,
      publishDate: publishDate ?? this.publishDate,
      isFavorite: isFavorite ?? this.isFavorite,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      readingProgress: readingProgress ?? this.readingProgress,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'coverUrl': coverUrl,
      'rating': rating,
      'category': category,
      'description': description,
      'pageCount': pageCount,
      'publishDate': publishDate,
      'isFavorite': isFavorite,
      'isDownloaded': isDownloaded,
      'readingProgress': readingProgress,
    };
  }

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'],
      title: json['title'],
      author: json['author'],
      coverUrl: json['coverUrl'],
      rating: json['rating'].toDouble(),
      category: json['category'],
      description: json['description'],
      pageCount: json['pageCount'],
      publishDate: json['publishDate'],
      isFavorite: json['isFavorite'] ?? false,
      isDownloaded: json['isDownloaded'] ?? false,
      readingProgress: json['readingProgress']?.toDouble() ?? 0.0,
    );
  }
}
