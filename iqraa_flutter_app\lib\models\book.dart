class Book {
  final String id;
  final String title;
  final String author;
  final String coverUrl;
  final double rating;
  final int reviewCount;
  final String category;
  final String? description;
  final int? pageCount;
  final String? publishDate;
  final bool isFavorite;
  final bool isDownloaded;
  final double readingProgress;
  final String? fileUrl;
  final String? language;
  final List<String> tags;
  final DateTime addedDate;
  final DateTime? lastReadDate;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.coverUrl,
    required this.rating,
    required this.reviewCount,
    required this.category,
    this.description,
    this.pageCount,
    this.publishDate,
    this.isFavorite = false,
    this.isDownloaded = false,
    this.readingProgress = 0.0,
    this.fileUrl,
    this.language = 'العربية',
    this.tags = const [],
    DateTime? addedDate,
    this.lastReadDate,
  }) : addedDate = addedDate ?? DateTime.now();

  Book copyWith({
    String? id,
    String? title,
    String? author,
    String? coverUrl,
    double? rating,
    int? reviewCount,
    String? category,
    String? description,
    int? pageCount,
    String? publishDate,
    bool? isFavorite,
    bool? isDownloaded,
    double? readingProgress,
    String? fileUrl,
    String? language,
    List<String>? tags,
    DateTime? addedDate,
    DateTime? lastReadDate,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      coverUrl: coverUrl ?? this.coverUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      category: category ?? this.category,
      description: description ?? this.description,
      pageCount: pageCount ?? this.pageCount,
      publishDate: publishDate ?? this.publishDate,
      isFavorite: isFavorite ?? this.isFavorite,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      readingProgress: readingProgress ?? this.readingProgress,
      fileUrl: fileUrl ?? this.fileUrl,
      language: language ?? this.language,
      tags: tags ?? this.tags,
      addedDate: addedDate ?? this.addedDate,
      lastReadDate: lastReadDate ?? this.lastReadDate,
    );
  }

  // Sample books data
  static List<Book> getSampleBooks() {
    return [
      Book(
        id: '1',
        title: 'مئة عام من العزلة',
        author: 'غابرييل غارسيا ماركيز',
        coverUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=400&fit=crop',
        rating: 4.5,
        reviewCount: 1250,
        category: 'الروايات',
        description: 'رواية للكاتب الكولومبي غابرييل غارسيا ماركيز، نُشرت عام 1967. تُعتبر من أهم الأعمال في الأدب اللاتيني الأمريكي وأحد أبرز أعمال الواقعية السحرية.',
        pageCount: 417,
        publishDate: '1967',
        language: 'مترجم',
        tags: ['واقعية سحرية', 'أدب لاتيني', 'كلاسيكي'],
        readingProgress: 0.3,
      ),
      Book(
        id: '2',
        title: 'الأسود يليق بك',
        author: 'أحلام مستغانمي',
        coverUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop',
        rating: 4.2,
        reviewCount: 890,
        category: 'الأدب العربي',
        description: 'رواية عربية معاصرة تتناول قصة حب معقدة في إطار اجتماعي وسياسي متغير.',
        pageCount: 350,
        publishDate: '2012',
        language: 'العربية',
        tags: ['رومانسي', 'معاصر', 'عربي'],
        isFavorite: true,
        readingProgress: 0.7,
      ),
      Book(
        id: '3',
        title: 'تاريخ الطبري',
        author: 'محمد بن جرير الطبري',
        coverUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop',
        rating: 4.8,
        reviewCount: 456,
        category: 'التاريخ',
        description: 'كتاب تاريخي شامل يغطي تاريخ العالم من بداية الخلق حتى عصر المؤلف.',
        pageCount: 2800,
        publishDate: '915',
        language: 'العربية',
        tags: ['تاريخ إسلامي', 'مرجع', 'تراث'],
        isDownloaded: true,
      ),
      Book(
        id: '4',
        title: 'مدن الملح',
        author: 'عبد الرحمن منيف',
        coverUrl: 'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=300&h=400&fit=crop',
        rating: 4.6,
        reviewCount: 723,
        category: 'الأدب العربي',
        description: 'خماسية روائية تصور التحولات الاجتماعية والاقتصادية في المنطقة العربية.',
        pageCount: 650,
        publishDate: '1984',
        language: 'العربية',
        tags: ['سياسي', 'اجتماعي', 'نفط'],
      ),
      Book(
        id: '5',
        title: 'كليلة ودمنة',
        author: 'ابن المقفع',
        coverUrl: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop',
        rating: 4.4,
        reviewCount: 334,
        category: 'الأدب الكلاسيكي',
        description: 'مجموعة من الحكايات والأمثال على ألسنة الحيوانات، مترجمة من الهندية.',
        pageCount: 280,
        publishDate: '750',
        language: 'العربية',
        tags: ['حكايات', 'تراث', 'حكمة'],
        isFavorite: true,
      ),
      Book(
        id: '6',
        title: 'الأيام',
        author: 'طه حسين',
        coverUrl: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=400&fit=crop',
        rating: 4.7,
        reviewCount: 567,
        category: 'السيرة الذاتية',
        description: 'سيرة ذاتية لعميد الأدب العربي طه حسين، تحكي قصة طفولته وشبابه.',
        pageCount: 320,
        publishDate: '1929',
        language: 'العربية',
        tags: ['سيرة ذاتية', 'تعليم', 'إلهام'],
        readingProgress: 0.1,
      ),
    ];
  }
}
