package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.data.model.NoteType
import com.iqraa.library.ui.components.NoteCard
import com.iqraa.library.ui.components.QuoteCard
import com.iqraa.library.ui.viewmodel.NoteViewModel
import com.iqraa.library.ui.viewmodel.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotesScreen(
    bookId: String? = null,
    onBackClick: () -> Unit,
    noteViewModel: NoteViewModel = hiltViewModel(),
    userViewModel: UserViewModel = hiltViewModel()
) {
    val currentUser by userViewModel.currentUser.collectAsState(initial = null)
    var selectedTab by remember { mutableStateOf(0) }
    
    val tabs = listOf(
        "الملاحظات",
        "التظليلات", 
        "العلامات المرجعية",
        "الاقتباسات"
    )
    
    val notes by if (bookId != null) {
        noteViewModel.getNotesForBook(bookId, currentUser?.id ?: "").collectAsState(initial = emptyList())
    } else {
        noteViewModel.getAllUserNotes(currentUser?.id ?: "").collectAsState(initial = emptyList())
    }
    
    val highlights by if (bookId != null) {
        noteViewModel.getHighlights(bookId, currentUser?.id ?: "").collectAsState(initial = emptyList())
    } else {
        remember { mutableStateOf(emptyList()) }
    }
    
    val bookmarks by if (bookId != null) {
        noteViewModel.getBookmarks(bookId, currentUser?.id ?: "").collectAsState(initial = emptyList())
    } else {
        remember { mutableStateOf(emptyList()) }
    }
    
    val quotes by noteViewModel.getUserQuotes(currentUser?.id ?: "").collectAsState(initial = emptyList())

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top App Bar
        TopAppBar(
            title = { 
                Text(
                    if (bookId != null) "ملاحظات الكتاب" else "جميع الملاحظات"
                ) 
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = null
                    )
                }
            }
        )
        
        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            modifier = Modifier.fillMaxWidth()
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { 
                        Text(
                            text = title,
                            maxLines = 1
                        ) 
                    }
                )
            }
        }
        
        // Content based on selected tab
        when (selectedTab) {
            0 -> NotesTabContent(
                notes = notes.filter { it.noteType == NoteType.NOTE },
                onEditNote = { /* Handle edit */ },
                onDeleteNote = { noteViewModel.deleteNote(it) },
                onShareNote = { /* Handle share */ }
            )
            
            1 -> HighlightsTabContent(
                highlights = highlights,
                onEditHighlight = { /* Handle edit */ },
                onDeleteHighlight = { noteViewModel.deleteNote(it) },
                onShareHighlight = { /* Handle share */ }
            )
            
            2 -> BookmarksTabContent(
                bookmarks = bookmarks,
                onEditBookmark = { /* Handle edit */ },
                onDeleteBookmark = { noteViewModel.deleteNote(it) },
                onNavigateToPage = { /* Handle navigation */ }
            )
            
            3 -> QuotesTabContent(
                quotes = quotes,
                onShareQuote = { noteViewModel.shareQuote(it.id) },
                onLikeQuote = { noteViewModel.likeQuote(it.id) }
            )
        }
    }
}

@Composable
private fun NotesTabContent(
    notes: List<com.iqraa.library.data.model.Note>,
    onEditNote: (com.iqraa.library.data.model.Note) -> Unit,
    onDeleteNote: (com.iqraa.library.data.model.Note) -> Unit,
    onShareNote: (com.iqraa.library.data.model.Note) -> Unit
) {
    if (notes.isEmpty()) {
        EmptyStateContent(
            icon = Icons.Default.Note,
            title = "لا توجد ملاحظات",
            subtitle = "ابدأ بإضافة ملاحظات أثناء القراءة"
        )
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(notes) { note ->
                NoteCard(
                    note = note,
                    onEditClick = { onEditNote(note) },
                    onDeleteClick = { onDeleteNote(note) },
                    onShareClick = { onShareNote(note) }
                )
            }
        }
    }
}

@Composable
private fun HighlightsTabContent(
    highlights: List<com.iqraa.library.data.model.Note>,
    onEditHighlight: (com.iqraa.library.data.model.Note) -> Unit,
    onDeleteHighlight: (com.iqraa.library.data.model.Note) -> Unit,
    onShareHighlight: (com.iqraa.library.data.model.Note) -> Unit
) {
    if (highlights.isEmpty()) {
        EmptyStateContent(
            icon = Icons.Default.Highlight,
            title = "لا توجد تظليلات",
            subtitle = "قم بتظليل النصوص المهمة أثناء القراءة"
        )
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(highlights) { highlight ->
                NoteCard(
                    note = highlight,
                    onEditClick = { onEditHighlight(highlight) },
                    onDeleteClick = { onDeleteHighlight(highlight) },
                    onShareClick = { onShareHighlight(highlight) }
                )
            }
        }
    }
}

@Composable
private fun BookmarksTabContent(
    bookmarks: List<com.iqraa.library.data.model.Note>,
    onEditBookmark: (com.iqraa.library.data.model.Note) -> Unit,
    onDeleteBookmark: (com.iqraa.library.data.model.Note) -> Unit,
    onNavigateToPage: (Int) -> Unit
) {
    if (bookmarks.isEmpty()) {
        EmptyStateContent(
            icon = Icons.Default.Bookmark,
            title = "لا توجد علامات مرجعية",
            subtitle = "أضف علامات مرجعية للصفحات المهمة"
        )
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(bookmarks) { bookmark ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    onClick = { onNavigateToPage(bookmark.pageNumber) }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Bookmark,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = bookmark.content.ifBlank { "علامة مرجعية" },
                                style = MaterialTheme.typography.bodyLarge
                            )
                            
                            Text(
                                text = "صفحة ${bookmark.pageNumber}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        IconButton(onClick = { onDeleteBookmark(bookmark) }) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "Delete",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun QuotesTabContent(
    quotes: List<com.iqraa.library.data.model.Quote>,
    onShareQuote: (com.iqraa.library.data.model.Quote) -> Unit,
    onLikeQuote: (com.iqraa.library.data.model.Quote) -> Unit
) {
    if (quotes.isEmpty()) {
        EmptyStateContent(
            icon = Icons.Default.FormatQuote,
            title = "لا توجد اقتباسات",
            subtitle = "احفظ الاقتباسات المفضلة لديك"
        )
    } else {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(quotes) { quote ->
                QuoteCard(
                    quote = quote,
                    onShareClick = { onShareQuote(quote) },
                    onLikeClick = { onLikeQuote(quote) }
                )
            }
        }
    }
}

@Composable
private fun EmptyStateContent(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    subtitle: String
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
