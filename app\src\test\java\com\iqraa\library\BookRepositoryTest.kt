package com.iqraa.library

import com.iqraa.library.data.database.BookDao
import com.iqraa.library.data.database.CategoryDao
import com.iqraa.library.data.model.Book
import com.iqraa.library.data.model.Category
import com.iqraa.library.data.model.FileType
import com.iqraa.library.data.repository.BookRepository
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.MockitoAnnotations

class BookRepositoryTest {
    
    @Mock
    private lateinit var bookDao: BookDao
    
    @Mock
    private lateinit var categoryDao: CategoryDao
    
    private lateinit var bookRepository: BookRepository
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        bookRepository = BookRepository(bookDao, categoryDao)
    }
    
    @Test
    fun `getAllBooks returns flow of books`() = runTest {
        // Given
        val books = listOf(createSampleBook())
        `when`(bookDao.getAllBooks()).thenReturn(flowOf(books))
        
        // When
        val result = bookRepository.getAllBooks()
        
        // Then
        result.collect { bookList ->
            assertEquals(1, bookList.size)
            assertEquals("Test Book", bookList[0].title)
        }
    }
    
    @Test
    fun `insertBook calls dao and increments category count`() = runTest {
        // Given
        val book = createSampleBook()
        
        // When
        bookRepository.insertBook(book)
        
        // Then
        verify(bookDao).insertBook(book)
        verify(categoryDao).incrementBookCount(book.categoryId)
    }
    
    @Test
    fun `deleteBook calls dao and decrements category count`() = runTest {
        // Given
        val book = createSampleBook()
        
        // When
        bookRepository.deleteBook(book)
        
        // Then
        verify(bookDao).deleteBook(book)
        verify(categoryDao).decrementBookCount(book.categoryId)
    }
    
    @Test
    fun `toggleFavorite updates book favorite status`() = runTest {
        // Given
        val bookId = "test_book_id"
        val isFavorite = true
        
        // When
        bookRepository.toggleFavorite(bookId, isFavorite)
        
        // Then
        verify(bookDao).updateFavoriteStatus(bookId, isFavorite)
    }
    
    @Test
    fun `updateReadingProgress updates book progress`() = runTest {
        // Given
        val bookId = "test_book_id"
        val page = 50
        val progress = 0.5f
        
        // When
        bookRepository.updateReadingProgress(bookId, page, progress)
        
        // Then
        verify(bookDao).updateReadingProgress(bookId, page, progress)
    }
    
    private fun createSampleBook(): Book {
        return Book(
            id = "test_book_id",
            title = "Test Book",
            author = "Test Author",
            description = "Test Description",
            coverImageUrl = "https://example.com/cover.jpg",
            fileUrl = "https://example.com/book.pdf",
            fileType = FileType.PDF,
            categoryId = "test_category",
            categoryName = "Test Category",
            publishDate = "2023",
            pageCount = 100,
            fileSize = 1024000,
            rating = 4.5f,
            reviewCount = 10
        )
    }
    
    private fun createSampleCategory(): Category {
        return Category(
            id = "test_category",
            name = "Test Category",
            nameEn = "Test Category",
            description = "Test Category Description",
            bookCount = 1
        )
    }
}
