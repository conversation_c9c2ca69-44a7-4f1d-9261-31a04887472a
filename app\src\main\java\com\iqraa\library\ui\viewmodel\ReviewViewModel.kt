package com.iqraa.library.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.iqraa.library.data.model.Review
import com.iqraa.library.data.model.UserBookRating
import com.iqraa.library.data.repository.ReviewRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class ReviewViewModel @Inject constructor(
    private val reviewRepository: ReviewRepository
) : ViewModel() {
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    fun getReviewsForBook(bookId: String): Flow<List<Review>> = 
        reviewRepository.getReviewsForBook(bookId)
    
    fun getReviewsByUser(userId: String): Flow<List<Review>> = 
        reviewRepository.getReviewsByUser(userId)
    
    suspend fun getAverageRatingForBook(bookId: String): Float = 
        reviewRepository.getAverageRatingForBook(bookId)
    
    suspend fun getReviewCountForBook(bookId: String): Int = 
        reviewRepository.getReviewCountForBook(bookId)
    
    fun submitReview(
        bookId: String,
        userId: String,
        userName: String,
        userAvatar: String?,
        rating: Float,
        title: String,
        content: String,
        containsSpoilers: Boolean = false
    ) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val review = Review(
                    id = UUID.randomUUID().toString(),
                    bookId = bookId,
                    userId = userId,
                    userName = userName,
                    userAvatar = userAvatar,
                    rating = rating,
                    title = title,
                    content = content,
                    containsSpoilers = containsSpoilers
                )
                
                reviewRepository.insertReview(review)
                
                // Also save user rating separately
                val userRating = UserBookRating(
                    id = UUID.randomUUID().toString(),
                    userId = userId,
                    bookId = bookId,
                    rating = rating
                )
                
                reviewRepository.insertUserRating(userRating)
                
            } catch (e: Exception) {
                _error.value = "فشل في إرسال المراجعة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun updateReview(review: Review) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val updatedReview = review.copy(
                    updatedAt = System.currentTimeMillis()
                )
                reviewRepository.updateReview(updatedReview)
            } catch (e: Exception) {
                _error.value = "فشل في تحديث المراجعة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun deleteReview(review: Review) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                reviewRepository.deleteReview(review)
                // Also delete user rating
                reviewRepository.deleteUserRating(review.userId, review.bookId)
            } catch (e: Exception) {
                _error.value = "فشل في حذف المراجعة: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun rateBook(userId: String, bookId: String, rating: Float) {
        viewModelScope.launch {
            try {
                val userRating = UserBookRating(
                    id = UUID.randomUUID().toString(),
                    userId = userId,
                    bookId = bookId,
                    rating = rating
                )
                
                reviewRepository.insertUserRating(userRating)
            } catch (e: Exception) {
                _error.value = "فشل في تقييم الكتاب: ${e.message}"
            }
        }
    }
    
    suspend fun getUserRatingForBook(userId: String, bookId: String): UserBookRating? = 
        reviewRepository.getUserRatingForBook(userId, bookId)
    
    fun clearError() {
        _error.value = null
    }
}
