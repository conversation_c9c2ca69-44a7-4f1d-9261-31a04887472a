import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/book.dart';
import '../providers/app_provider.dart';
import '../utils/app_theme.dart';

class PdfReaderScreen extends StatefulWidget {
  final Book book;
  final String pdfPath;

  const PdfReaderScreen({
    super.key,
    required this.book,
    required this.pdfPath,
  });

  @override
  State<PdfReaderScreen> createState() => _PdfReaderScreenState();
}

class _PdfReaderScreenState extends State<PdfReaderScreen> {
  int _currentPage = 1;
  int _totalPages = 100; // Default for demo
  bool _isLoading = false;
  bool _showControls = true;
  double _zoomLevel = 1.0;

  @override
  void initState() {
    super.initState();
    // Simulate loading
    _loadPdf();
  }

  void _loadPdf() {
    // Simulate PDF loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _totalPages = 150; // Simulated page count
        });
        _updateReadingProgress();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // PDF Viewer Placeholder (Web-compatible)
          GestureDetector(
            onTap: () {
              setState(() {
                _showControls = !_showControls;
              });
            },
            child: Container(
              color: Colors.grey[100],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: 100,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'قارئ PDF',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                color: Colors.grey[600],
                              ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'الصفحة $_currentPage من $_totalPages',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[500],
                          ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'انقر على الشاشة لإخفاء/إظهار أدوات التحكم',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[400],
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Loading indicator
          if (_isLoading)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            ),

          // Top Controls
          if (_showControls)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        IconButton(
                          icon:
                              const Icon(Icons.arrow_back, color: Colors.white),
                          onPressed: () => Navigator.pop(context),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.book.title,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                widget.book.author,
                                style: const TextStyle(
                                  color: Colors.white70,
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.bookmark_border,
                              color: Colors.white),
                          onPressed: _addBookmark,
                        ),
                        IconButton(
                          icon: const Icon(Icons.share, color: Colors.white),
                          onPressed: _shareBook,
                        ),
                        IconButton(
                          icon:
                              const Icon(Icons.more_vert, color: Colors.white),
                          onPressed: _showOptionsMenu,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

          // Bottom Controls
          if (_showControls)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Progress bar
                        Row(
                          children: [
                            Text(
                              '$_currentPage',
                              style: const TextStyle(color: Colors.white),
                            ),
                            Expanded(
                              child: Slider(
                                value: _currentPage.toDouble(),
                                min: 1,
                                max: _totalPages.toDouble(),
                                onChanged: (value) {
                                  setState(() {
                                    _currentPage = value.toInt();
                                  });
                                  _updateReadingProgress();
                                },
                                activeColor: AppTheme.arabicGold,
                                inactiveColor: Colors.white30,
                              ),
                            ),
                            Text(
                              '$_totalPages',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),

                        // Control buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.zoom_out,
                                  color: Colors.white),
                              onPressed: () {
                                setState(() {
                                  _zoomLevel =
                                      (_zoomLevel - 0.25).clamp(0.5, 3.0);
                                });
                              },
                            ),
                            IconButton(
                              icon: const Icon(Icons.skip_previous,
                                  color: Colors.white),
                              onPressed:
                                  _currentPage > 1 ? _previousPage : null,
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.arabicBlue,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '$_currentPage / $_totalPages',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.skip_next,
                                  color: Colors.white),
                              onPressed:
                                  _currentPage < _totalPages ? _nextPage : null,
                            ),
                            IconButton(
                              icon: const Icon(Icons.zoom_in,
                                  color: Colors.white),
                              onPressed: () {
                                setState(() {
                                  _zoomLevel =
                                      (_zoomLevel + 0.25).clamp(0.5, 3.0);
                                });
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
      _updateReadingProgress();
    }
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
      _updateReadingProgress();
    }
  }

  void _updateReadingProgress() {
    if (_totalPages > 0) {
      final progress = _currentPage / _totalPages;
      context
          .read<AppProvider>()
          .updateReadingProgress(widget.book.id, progress);
    }
  }

  void _addBookmark() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة إشارة مرجعية للصفحة $_currentPage'),
        backgroundColor: AppTheme.arabicGreen,
      ),
    );
  }

  void _shareBook() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('مشاركة: ${widget.book.title} - الصفحة $_currentPage'),
      ),
    );
  }

  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.brightness_6),
              title: const Text('وضع القراءة الليلي'),
              trailing: Switch(
                value: false,
                onChanged: (value) {
                  // TODO: Implement night mode
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.text_fields),
              title: const Text('حجم النص'),
              onTap: () {
                Navigator.pop(context);
                _showTextSizeDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('الإشارات المرجعية'),
              onTap: () {
                Navigator.pop(context);
                _showBookmarks();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('معلومات الكتاب'),
              onTap: () {
                Navigator.pop(context);
                _showBookInfo();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showTextSizeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حجم النص'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('مستوى التكبير: ${_zoomLevel.toStringAsFixed(1)}x'),
            Slider(
              value: _zoomLevel,
              min: 0.5,
              max: 3.0,
              divisions: 10,
              onChanged: (value) {
                setState(() {
                  _zoomLevel = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showBookmarks() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشارات المرجعية'),
        content: const Text('لا توجد إشارات مرجعية محفوظة'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showBookInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.book.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المؤلف: ${widget.book.author}'),
            Text('التصنيف: ${widget.book.category}'),
            Text('عدد الصفحات: $_totalPages'),
            Text('الصفحة الحالية: $_currentPage'),
            Text(
                'التقدم: ${((_currentPage / _totalPages) * 100).toStringAsFixed(1)}%'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
