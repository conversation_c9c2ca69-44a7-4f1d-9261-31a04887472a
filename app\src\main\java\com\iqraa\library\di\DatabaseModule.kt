package com.iqraa.library.di

import android.content.Context
import androidx.room.Room
import com.iqraa.library.data.database.*
import com.iqraa.library.data.model.DefaultCategories
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        val database = Room.databaseBuilder(
            context.applicationContext,
            AppDatabase::class.java,
            "iqraa_database"
        )
        .fallbackToDestructiveMigration()
        .build()
        
        // Initialize default categories
        CoroutineScope(Dispatchers.IO).launch {
            val categoryDao = database.categoryDao()
            val existingCategories = categoryDao.getAllCategories()
            // Only insert if no categories exist
            try {
                categoryDao.insertCategories(DefaultCategories.categories)
            } catch (e: Exception) {
                // Categories might already exist
            }
        }
        
        return database
    }
    
    @Provides
    fun provideBookDao(database: AppDatabase): BookDao = database.bookDao()
    
    @Provides
    fun provideUserDao(database: AppDatabase): UserDao = database.userDao()
    
    @Provides
    fun provideCategoryDao(database: AppDatabase): CategoryDao = database.categoryDao()
    
    @Provides
    fun provideReviewDao(database: AppDatabase): ReviewDao = database.reviewDao()
    
    @Provides
    fun provideNoteDao(database: AppDatabase): NoteDao = database.noteDao()
}
