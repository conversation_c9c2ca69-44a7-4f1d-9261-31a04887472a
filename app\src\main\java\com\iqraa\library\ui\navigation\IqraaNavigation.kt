package com.iqraa.library.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.iqraa.library.R
import com.iqraa.library.ui.screens.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IqraaNavigation() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentDestination = navBackStackEntry?.destination

    val bottomNavItems = listOf(
        BottomNavItem(
            route = "home",
            icon = Icons.Default.Home,
            labelRes = R.string.nav_home
        ),
        BottomNavItem(
            route = "library",
            icon = Icons.Default.LibraryBooks,
            labelRes = R.string.nav_library
        ),
        BottomNavItem(
            route = "categories",
            icon = Icons.Default.Category,
            labelRes = R.string.nav_categories
        ),
        BottomNavItem(
            route = "search",
            icon = Icons.Default.Search,
            labelRes = R.string.nav_search
        ),
        BottomNavItem(
            route = "profile",
            icon = Icons.Default.Person,
            labelRes = R.string.nav_profile
        )
    )

    Scaffold(
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = { Icon(item.icon, contentDescription = null) },
                        label = { Text(stringResource(item.labelRes)) },
                        selected = currentDestination?.hierarchy?.any { it.route == item.route } == true,
                        onClick = {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        }
                    )
                }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "home",
            modifier = Modifier.padding(innerPadding)
        ) {
            composable("home") {
                HomeScreen(
                    onBookClick = { bookId ->
                        navController.navigate("book_detail/$bookId")
                    },
                    onCategoryClick = { categoryId ->
                        navController.navigate("category/$categoryId")
                    }
                )
            }
            
            composable("library") {
                LibraryScreen(
                    onBookClick = { bookId ->
                        navController.navigate("book_detail/$bookId")
                    }
                )
            }
            
            composable("categories") {
                CategoriesScreen(
                    onCategoryClick = { categoryId ->
                        navController.navigate("category/$categoryId")
                    }
                )
            }
            
            composable("search") {
                SearchScreen(
                    onBookClick = { bookId ->
                        navController.navigate("book_detail/$bookId")
                    }
                )
            }
            
            composable("profile") {
                ProfileScreen(
                    onSettingsClick = {
                        navController.navigate("settings")
                    }
                )
            }
            
            composable("book_detail/{bookId}") { backStackEntry ->
                val bookId = backStackEntry.arguments?.getString("bookId") ?: ""
                BookDetailScreen(
                    bookId = bookId,
                    onBackClick = { navController.popBackStack() },
                    onReadClick = { bookId ->
                        navController.navigate("reader/$bookId")
                    }
                )
            }
            
            composable("category/{categoryId}") { backStackEntry ->
                val categoryId = backStackEntry.arguments?.getString("categoryId") ?: ""
                CategoryBooksScreen(
                    categoryId = categoryId,
                    onBackClick = { navController.popBackStack() },
                    onBookClick = { bookId ->
                        navController.navigate("book_detail/$bookId")
                    }
                )
            }
            
            composable("reader/{bookId}") { backStackEntry ->
                val bookId = backStackEntry.arguments?.getString("bookId") ?: ""
                ReaderScreen(
                    bookId = bookId,
                    onBackClick = { navController.popBackStack() }
                )
            }
            
            composable("settings") {
                SettingsScreen(
                    onBackClick = { navController.popBackStack() }
                )
            }
        }
    }
}

data class BottomNavItem(
    val route: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector,
    val labelRes: Int
)
