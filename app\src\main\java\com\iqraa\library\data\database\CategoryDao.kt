package com.iqraa.library.data.database

import androidx.room.*
import com.iqraa.library.data.model.Category
import kotlinx.coroutines.flow.Flow

@Dao
interface CategoryDao {
    
    @Query("SELECT * FROM categories ORDER BY sortOrder ASC, name ASC")
    fun getAllCategories(): Flow<List<Category>>
    
    @Query("SELECT * FROM categories WHERE isPopular = 1 ORDER BY sortOrder ASC")
    fun getPopularCategories(): Flow<List<Category>>
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    suspend fun getCategoryById(categoryId: String): Category?
    
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    fun getCategoryByIdFlow(categoryId: String): Flow<Category?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategory(category: Category)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<Category>)
    
    @Update
    suspend fun updateCategory(category: Category)
    
    @Delete
    suspend fun deleteCategory(category: Category)
    
    @Query("UPDATE categories SET bookCount = bookCount + 1 WHERE id = :categoryId")
    suspend fun incrementBookCount(categoryId: String)
    
    @Query("UPDATE categories SET bookCount = bookCount - 1 WHERE id = :categoryId AND bookCount > 0")
    suspend fun decrementBookCount(categoryId: String)
}
