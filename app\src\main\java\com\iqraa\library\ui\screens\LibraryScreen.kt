package com.iqraa.library.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.iqraa.library.R
import com.iqraa.library.ui.components.BookCard
import com.iqraa.library.ui.viewmodel.BookViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LibraryScreen(
    onBookClick: (String) -> Unit,
    bookViewModel: BookViewModel = hiltViewModel()
) {
    var selectedTab by remember { mutableStateOf(0) }
    
    val tabs = listOf(
        stringResource(R.string.downloaded_books),
        stringResource(R.string.favorite_books),
        stringResource(R.string.currently_reading),
        stringResource(R.string.completed_books)
    )
    
    val downloadedBooks by bookViewModel.downloadedBooks.collectAsState(initial = emptyList())
    val favoriteBooks by bookViewModel.favoriteBooks.collectAsState(initial = emptyList())
    val currentlyReadingBooks by bookViewModel.currentlyReadingBooks.collectAsState(initial = emptyList())
    val completedBooks by bookViewModel.completedBooks.collectAsState(initial = emptyList())

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Header
        Text(
            text = stringResource(R.string.my_library),
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(16.dp)
        )
        
        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            modifier = Modifier.fillMaxWidth()
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { 
                        Text(
                            text = title,
                            maxLines = 1
                        ) 
                    }
                )
            }
        }
        
        // Content
        val currentBooks = when (selectedTab) {
            0 -> downloadedBooks
            1 -> favoriteBooks
            2 -> currentlyReadingBooks
            3 -> completedBooks
            else -> emptyList()
        }
        
        if (currentBooks.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = getEmptyMessage(selectedTab),
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = getEmptySubMessage(selectedTab),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(16.dp)
            ) {
                items(currentBooks) { book ->
                    BookCard(
                        book = book,
                        onClick = { onBookClick(book.id) },
                        showProgress = selectedTab == 2, // Show progress for currently reading
                        onFavoriteClick = { isFavorite ->
                            bookViewModel.toggleFavorite(book.id, isFavorite)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun getEmptyMessage(tabIndex: Int): String {
    return when (tabIndex) {
        0 -> "لا توجد كتب محملة"
        1 -> "لا توجد كتب مفضلة"
        2 -> "لا توجد كتب قيد القراءة"
        3 -> "لا توجد كتب مكتملة"
        else -> ""
    }
}

@Composable
private fun getEmptySubMessage(tabIndex: Int): String {
    return when (tabIndex) {
        0 -> "حمل كتبك المفضلة للقراءة دون اتصال"
        1 -> "أضف كتبك المفضلة لتجدها هنا"
        2 -> "ابدأ قراءة كتاب جديد"
        3 -> "أكمل قراءة كتاب لتجده هنا"
        else -> ""
    }
}
